import argparse
import os
import time

import numpy as np
import torch
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from loss.distortion import MS_SSIMLoss
from models.channel import Channel as CommunicationChannel
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import (
    AverageMeter,
    cal_angle,
    load_individual_model_state,
    logger_configuration,
    save_model_checkpoint,
    seed_torch,
)

# Global variable for MS-SSIM calculator
ms_ssim_calculator = None


def setup_ms_ssim_calculator(cfg):
    global ms_ssim_calculator
    if cfg.distortion_metric == "MS-SSIM":
        window_s = 3 if cfg.trainset == "CIFAR10" else 7
        ms_ssim_calculator = MS_SSIMLoss(
            window_size=window_s, data_range=1.0, channel=3, use_padding=True, levels=4
        )
        if cfg.CUDA:
            ms_ssim_calculator = ms_ssim_calculator.cuda()


def parse_args():
    parser = argparse.ArgumentParser(
        description="Semantic Communication System Training"
    )
    # 实验相关参数
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=1, choices=[1, 2, 3])
    parser.add_argument(
        "--run_all_phases", action="store_true", help="运行所有三个阶段的训练"
    )
    parser.add_argument("--test_only", action="store_true")

    # 数据集相关参数
    parser.add_argument(
        "--trainset",
        type=str,
        default="celeba",
        choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"],
    )
    parser.add_argument(
        "--testset", type=str, default="kodak", choices=["kodak", "CLIC21"]
    )
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)

    # 模型相关参数
    parser.add_argument("--model", type=str, default="WITT_W/O", choices=["WITT_W/O"])
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)

    # 训练相关参数
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)

    # 通道相关参数
    parser.add_argument(
        "--channel_type",
        type=str,
        default="rayleigh",
        choices=["awgn", "rayleigh", "none"],
    )
    parser.add_argument("--multiple_snr", type=str, default="5")

    # 损失函数相关参数
    parser.add_argument(
        "--distortion_metric", type=str, default="MSE", choices=["MSE", "MS-SSIM"]
    )

    # 模型加载相关参数
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)

    return parser.parse_args()


def get_dataloaders(cfg):
    data_root = cfg.data_path
    img_h, img_w = cfg.image_dims[1], cfg.image_dims[2]

    transform = transforms.Compose(
        [
            transforms.Resize((img_h, img_w)),
            transforms.RandomHorizontalFlip(),
            transforms.ToTensor(),
        ]
    )

    test_transform = transforms.Compose(
        [
            transforms.Resize((img_h, img_w)),
            transforms.ToTensor(),
        ]
    )

    if cfg.trainset == "CIFAR10":
        transform_train = transforms.Compose(
            [
                transforms.RandomCrop(32, padding=4),
                transforms.RandomHorizontalFlip(),
                transforms.ToTensor(),
            ]
        )
        transform_test = transforms.Compose([transforms.ToTensor()])
        train_dataset = datasets.CIFAR10(
            root="./data_cifar10", train=True, download=True, transform=transform_train
        )
        val_dataset = datasets.CIFAR10(
            root="./data_cifar10", train=False, download=True, transform=transform_test
        )
        test_dataset = val_dataset
    else:
        train_dataset = datasets.ImageFolder(root=data_root, transform=transform)
        val_dataset = datasets.ImageFolder(root=data_root, transform=test_transform)
        test_dataset = datasets.ImageFolder(root=data_root, transform=test_transform)

    train_loader_u1 = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=True,
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )
    train_loader_u2 = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=True,
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=cfg.test_batch_size,
        shuffle=False,
        num_workers=cfg.num_workers,
        pin_memory=True,
    )
    test_loader = DataLoader(
        test_dataset,
        batch_size=cfg.test_batch_size,
        shuffle=False,
        num_workers=cfg.num_workers,
        pin_memory=True,
    )

    return train_loader_u1, train_loader_u2, val_loader, test_loader


def train_one_epoch(
    epoch,
    net1,
    net2,
    quantizer,
    interleaver,
    channel_sim,
    train_loader_u1,
    train_loader_u2,
    optimizer_core,
    optimizer_quant,
    cfg,
    logger,
    training_phase,
):
    net1.train()
    net2.train()
    if quantizer:
        quantizer.train()

    meters = {
        name: AverageMeter()
        for name in [
            "elapsed",
            "loss_total",
            "loss_g1",
            "loss_g2",
            "psnr1",
            "psnr2",
            "msssim1",
            "msssim2",
            "cbr",
            "snr_log",
            "angle",
        ]
    }

    for batch_idx, ((input_u1, _), (input_u2, _)) in enumerate(
        zip(train_loader_u1, train_loader_u2)
    ):
        start_time = time.time()
        input_u1, input_u2 = input_u1.to(cfg.device), input_u2.to(cfg.device)

        features_u1 = net1.encode(input_u1)
        features_u2 = net2.encode(input_u2)

        angle = cal_angle(features_u1, features_u2)
        if not torch.isnan(angle):
            meters["angle"].update(angle.item())

        B1, L1_feat, D1_feat = features_u1.shape
        B2, L2_feat, D2_feat = features_u2.shape

        if training_phase > 1 and quantizer:
            features = features_u1 + features_u2
            features_Q = quantizer.quantize_features_to_bits(
                features.reshape(B1 * L1_feat, D1_feat)
            )
            feature_for_channel = features_Q.reshape(B1, L1_feat, cfg.quant_code_dim)
        else:
            feature_for_channel = features_u1 + features_u2

        # Apply interleaving
        feature_for_channel, interleaver_metadata = interleaver.interleave(
            feature_for_channel
        )

        snr_for_channel = float(np.random.choice(cfg.multiple_snr_list))
        h1_rayleigh = h2_rayleigh = torch.tensor(
            1.0, device=cfg.device, dtype=torch.complex64
        )

        if cfg.channel_type == "rayleigh":
            h1_rayleigh = torch.sqrt(torch.tensor(0.5, device=cfg.device)) * (
                torch.randn(1, device=cfg.device, dtype=torch.complex64)
                + 1j * torch.randn(1, device=cfg.device, dtype=torch.complex64)
            )
            h2_rayleigh = torch.sqrt(torch.tensor(0.5, device=cfg.device)) * (
                torch.randn(1, device=cfg.device, dtype=torch.complex64)
                + 1j * torch.randn(1, device=cfg.device, dtype=torch.complex64)
            )

        noisy_output_path1, noisy_output_path2 = channel_sim(
            feature_for_channel,
            h1_rayleigh,
            h2_rayleigh,
            p1_power_factor=1.0,
            p2_power_factor=1.0,
            snr_db=snr_for_channel,
        )

        # Deinterleave before decoding
        noisy_output_path1 = interleaver.deinterleave(
            noisy_output_path1, metadata=interleaver_metadata
        )

        noisy_output_path2 = interleaver.deinterleave(
            noisy_output_path2, metadata=interleaver_metadata
        )

        if training_phase > 1 and quantizer:
            noisy_sum_bits_p1 = noisy_output_path1.reshape(
                B1 * L1_feat, cfg.quant_code_dim
            )
            noisy_sum_bits_p2 = noisy_output_path2.reshape(
                B2 * L2_feat, cfg.quant_code_dim
            )

            final_features_for_dec1 = quantizer.dequantize_bits_to_features(
                noisy_sum_bits_p1
            ).reshape(B1, L1_feat, D1_feat)
            final_features_for_dec2 = quantizer.dequantize_bits_to_features(
                noisy_sum_bits_p2
            ).reshape(B2, L2_feat, D2_feat)
        else:
            final_features_for_dec1 = noisy_output_path1.reshape(B1, L1_feat, D1_feat)
            final_features_for_dec2 = noisy_output_path2.reshape(B2, L2_feat, D2_feat)

        recon_image_u1 = net1.decode(final_features_for_dec1)
        recon_image_u2 = net2.decode(final_features_for_dec2)

        loss_g1 = net1.calculate_loss(input_u1, recon_image_u1)
        loss_g2 = net2.calculate_loss(input_u2, recon_image_u2)
        total_loss = loss_g1 + loss_g2

        if optimizer_core:
            optimizer_core.zero_grad()
        if optimizer_quant:
            optimizer_quant.zero_grad()

        total_loss.backward()

        if training_phase == 1:
            optimizer_core.step()
        elif training_phase == 2:
            optimizer_quant.step()
        elif training_phase == 3:
            optimizer_core.step()
            optimizer_quant.step()

        meters["elapsed"].update(time.time() - start_time)
        meters["loss_total"].update(total_loss.item())
        meters["loss_g1"].update(loss_g1.item())
        meters["loss_g2"].update(loss_g2.item())
        meters["snr_log"].update(snr_for_channel)

        psnr_u1 = net1.calculate_psnr(input_u1, recon_image_u1)
        psnr_u2 = net2.calculate_psnr(input_u2, recon_image_u2)
        meters["psnr1"].update(psnr_u1.item())
        meters["psnr2"].update(psnr_u2.item())

        if ms_ssim_calculator:
            with torch.no_grad():
                msssim_u1_val = (
                    1.0
                    - ms_ssim_calculator(
                        input_u1, recon_image_u1.clamp(0.0, 1.0)
                    ).mean()
                )
                msssim_u2_val = (
                    1.0
                    - ms_ssim_calculator(
                        input_u2, recon_image_u2.clamp(0.0, 1.0)
                    ).mean()
                )
            meters["msssim1"].update(msssim_u1_val.item())
            meters["msssim2"].update(msssim_u2_val.item())

        if training_phase > 1 and quantizer:
            cbr_val = (L1_feat * cfg.quant_code_dim) / (
                input_u1.shape[1] * input_u1.shape[2] * input_u1.shape[3] * 8
            )
        else:
            cbr_val = (L1_feat * D1_feat * 32) / (
                input_u1.shape[1] * input_u1.shape[2] * input_u1.shape[3] * 8
            )
        meters["cbr"].update(cbr_val)

        if (batch_idx + 1) % cfg.print_step == 0:
            log_str = f"Epoch {epoch + 1}/{cfg.tot_epoch} | Batch {batch_idx + 1}/{len(train_loader_u1)} | "
            if optimizer_core:
                log_str += f"LR_core: {optimizer_core.param_groups[0]['lr']:.1e} | "
            if optimizer_quant:
                log_str += f"LR_quant: {optimizer_quant.param_groups[0]['lr']:.1e} | "
            for name, meter in meters.items():
                if meter.count > 0:
                    log_str += f"{name}: {meter.avg:.3f} | "
            logger.info(log_str[:-3])
            for meter_val in meters.values():
                meter_val.clear()

    epoch_log_str = f"End of Epoch {epoch + 1}/{cfg.tot_epoch} | "
    epoch_log_str += f"Avg Loss: {meters['loss_total'].avg:.4f} | "
    epoch_log_str += f"Avg PSNR1: {meters['psnr1'].avg:.2f} dB | "
    epoch_log_str += f"Avg PSNR2: {meters['psnr2'].avg:.2f} dB"
    if ms_ssim_calculator:
        epoch_log_str += f" | Avg MS-SSIM1: {meters['msssim1'].avg:.4f} | Avg MS-SSIM2: {meters['msssim2'].avg:.4f}"
    if meters["angle"].count > 0:
        epoch_log_str += f" | Avg Angle: {meters['angle'].avg:.2f}"
    logger.info(epoch_log_str)

    return meters["loss_total"].avg


def train_single_phase(phase, args, base_experiment_name, pretrained_path=None):
    """训练单个阶段"""
    print(f"\n{'=' * 50}")
    print(f"开始训练阶段 {phase}")
    print(f"{'=' * 50}")

    # 为当前阶段创建配置
    cfg = BaseConfig(args, training_phase=phase)

    # 设置随机种子
    seed_torch(cfg.seed)

    # 配置日志
    logger = logger_configuration(
        cfg.log_file_path,
        cfg.workdir_base,
        cfg.samples_dir,
        cfg.models_dir,
        save_log=True,
    )

    logger.info(f"开始训练阶段 {phase}")
    logger.info(f"实验名称: {base_experiment_name}")
    logger.info(f"工作目录: {cfg.workdir_base}")

    # 设置MS-SSIM计算器
    setup_ms_ssim_calculator(cfg)

    # 获取数据加载器
    train_loader1, train_loader2, val_loader, test_loader = get_dataloaders(cfg)

    # 初始化模型
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)

    # 初始化量化器（如果需要）
    quantizer = None
    optimizer_quant = None
    if phase > 1:
        q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
        quantizer = DENSEQuantizer(
            feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim
        ).to(cfg.device)
        optimizer_quant = optim.Adam(quantizer.parameters(), lr=args.lr_quantizer)

    # 初始化交织器（独立组件）
    interleaver = BlockInterleaver().to(cfg.device)

    # 初始化通道模拟器
    channel_simulator = CommunicationChannel(args, cfg).to(cfg.device)

    # 初始化优化器
    params_core = list(net1.parameters()) + list(net2.parameters())
    optimizer_core = optim.Adam(params_core, lr=cfg.learning_rate)

    # 设置参数是否需要梯度
    if phase == 1:
        for param in net1.parameters():
            param.requires_grad = True
        for param in net2.parameters():
            param.requires_grad = True
    elif phase == 2:
        for param in net1.parameters():
            param.requires_grad = False
        for param in net2.parameters():
            param.requires_grad = False
        if quantizer:
            for param in quantizer.parameters():
                param.requires_grad = True
    elif phase == 3:
        for param in net1.parameters():
            param.requires_grad = True
        for param in net2.parameters():
            param.requires_grad = True
        if quantizer:
            for param in quantizer.parameters():
                param.requires_grad = True

    # 加载预训练模型（如果有）
    start_epoch = 0
    if pretrained_path and os.path.isfile(pretrained_path):
        logger.info(f"加载预训练模型: {pretrained_path}")
        checkpoint = torch.load(pretrained_path, map_location=cfg.device)
        if "net1_state_dict" in checkpoint:
            load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
            load_individual_model_state(net2, checkpoint["net1_state_dict"], cfg.device)
        if quantizer and "quantizer_state_dict" in checkpoint:
            load_individual_model_state(
                quantizer, checkpoint["quantizer_state_dict"], cfg.device
            )

    # 恢复检查点（如果有）
    if args.resume_checkpoint and os.path.isfile(args.resume_checkpoint):
        logger.info(f"恢复检查点: {args.resume_checkpoint}")
        checkpoint = torch.load(args.resume_checkpoint, map_location=cfg.device)
        start_epoch = checkpoint.get("epoch", 0)
        if "net1_state_dict" in checkpoint:
            load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
        if "net2_state_dict" in checkpoint:
            load_individual_model_state(net2, checkpoint["net2_state_dict"], cfg.device)
        if quantizer and "quantizer_state_dict" in checkpoint:
            load_individual_model_state(
                quantizer, checkpoint["quantizer_state_dict"], cfg.device
            )
        if optimizer_core and "optimizer_core_state_dict" in checkpoint:
            optimizer_core.load_state_dict(checkpoint["optimizer_core_state_dict"])
        if optimizer_quant and "optimizer_quant_state_dict" in checkpoint:
            optimizer_quant.load_state_dict(checkpoint["optimizer_quant_state_dict"])

    # 训练循环
    best_loss = float("inf")
    final_checkpoint_path = None

    if not args.test_only:
        for epoch in range(start_epoch, cfg.tot_epoch):
            avg_loss = train_one_epoch(
                epoch,
                net1,
                net2,
                quantizer,
                interleaver,
                channel_simulator,
                train_loader1,
                train_loader2,
                optimizer_core,
                optimizer_quant,
                cfg,
                logger,
                phase,
            )

            # 保存检查点
            if (epoch + 1) % cfg.save_model_freq == 0:
                save_filename = f"{cfg.trainset}_phase{phase}_epoch{epoch + 1}_snr{args.multiple_snr.replace(',', '-')}.pth"
                checkpoint_data = {
                    "epoch": epoch + 1,
                    "phase": phase,
                    "args": vars(args),
                    "config": vars(cfg),
                    "net1_state_dict": net1.state_dict(),
                    "net2_state_dict": net2.state_dict(),
                    "avg_loss": avg_loss,
                }
                if optimizer_core:
                    checkpoint_data["optimizer_core_state_dict"] = (
                        optimizer_core.state_dict()
                    )
                if quantizer:
                    checkpoint_data["quantizer_state_dict"] = quantizer.state_dict()
                if optimizer_quant:
                    checkpoint_data["optimizer_quant_state_dict"] = (
                        optimizer_quant.state_dict()
                    )

                checkpoint_path = os.path.join(cfg.models_dir, save_filename)
                save_model_checkpoint(
                    checkpoint_data, cfg.models_dir, filename=save_filename
                )
                logger.info(f"保存检查点到: {checkpoint_path}")

                # 记录最佳模型
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    final_checkpoint_path = checkpoint_path

    logger.info(f"阶段 {phase} 训练完成")
    logger.info(f"最佳损失: {best_loss:.4f}")

    return final_checkpoint_path


if __name__ == "__main__":
    # 解析命令行参数
    args = parse_args()

    if args.run_all_phases:
        # 运行所有三个阶段
        print("开始连续训练所有三个阶段...")

        # 保存原始实验名称
        base_experiment_name = args.experiment_name

        # 阶段1: 训练核心网络
        print(f"\n{'=' * 60}")
        print("阶段1: 训练核心网络 (编码器和解码器)")
        print(f"{'=' * 60}")

        phase1_checkpoint = train_single_phase(1, args, base_experiment_name)

        # 阶段2: 训练量化器
        print(f"\n{'=' * 60}")
        print("阶段2: 训练量化器")
        print(f"{'=' * 60}")

        phase2_checkpoint = train_single_phase(
            2, args, base_experiment_name, phase1_checkpoint
        )

        # 阶段3: 联合微调
        print(f"\n{'=' * 60}")
        print("阶段3: 联合微调所有组件")
        print(f"{'=' * 60}")

        phase3_checkpoint = train_single_phase(
            3, args, base_experiment_name, phase2_checkpoint
        )

        print(f"\n{'=' * 60}")
        print("所有阶段训练完成!")
        print(f"阶段1最终模型: {phase1_checkpoint}")
        print(f"阶段2最终模型: {phase2_checkpoint}")
        print(f"阶段3最终模型: {phase3_checkpoint}")
        print(f"{'=' * 60}")

    else:
        # 运行单个阶段
        base_experiment_name = args.experiment_name
        pretrained_path = args.load_pretrained_core

        final_checkpoint = train_single_phase(
            args.training_phase, args, base_experiment_name, pretrained_path
        )

        print(f"\n阶段 {args.training_phase} 训练完成!")
        print(f"最终模型保存在: {final_checkpoint}")

    print("训练/测试完成.")
