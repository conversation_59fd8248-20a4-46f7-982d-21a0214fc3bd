import torch
import torch.onnx
import onnxruntime as ort
import numpy as np


class SimpleSignModel(torch.nn.Module):
    def __init__(self):
        super().__init__()
        
    def forward(self, x):
        return torch.sign(x)


class SimpleQuantizerModel(torch.nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = torch.nn.Linear(4, 4)
        
    def forward(self, x):
        y = self.linear(x)
        return torch.sign(y)


def test_sign_onnx():
    print("测试torch.sign在ONNX中的行为...")
    
    # 创建测试模型
    model = SimpleSignModel()
    model.eval()
    
    # 创建测试数据
    test_input = torch.tensor([[-2.0, -0.5, 0.0, 0.5, 2.0]], dtype=torch.float32)
    
    # PyTorch推理
    with torch.no_grad():
        pytorch_output = model(test_input)
    
    print(f"输入: {test_input}")
    print(f"PyTorch输出: {pytorch_output}")
    
    # 导出ONNX
    torch.onnx.export(
        model,
        test_input,
        "simple_sign.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input"],
        output_names=["output"]
    )
    
    # ONNX推理
    session = ort.InferenceSession("simple_sign.onnx")
    onnx_output = session.run(None, {"input": test_input.numpy()})[0]
    
    print(f"ONNX输出: {onnx_output}")
    print(f"差异: {np.abs(pytorch_output.numpy() - onnx_output).max()}")
    
    return np.allclose(pytorch_output.numpy(), onnx_output, atol=1e-6)


def test_quantizer_onnx():
    print("\n测试量化器在ONNX中的行为...")
    
    # 创建测试模型
    model = SimpleQuantizerModel()
    model.eval()
    
    # 设置固定权重以便复现
    with torch.no_grad():
        model.linear.weight.data = torch.tensor([
            [1.0, -1.0, 0.5, -0.5],
            [-1.0, 1.0, -0.5, 0.5],
            [0.5, -0.5, 1.0, -1.0],
            [-0.5, 0.5, -1.0, 1.0]
        ])
        model.linear.bias.data = torch.tensor([0.1, -0.1, 0.0, 0.0])
    
    # 创建测试数据
    test_input = torch.tensor([[1.0, -1.0, 0.5, -0.5]], dtype=torch.float32)
    
    # PyTorch推理
    with torch.no_grad():
        pytorch_output = model(test_input)
    
    print(f"输入: {test_input}")
    print(f"PyTorch输出: {pytorch_output}")
    
    # 导出ONNX
    torch.onnx.export(
        model,
        test_input,
        "simple_quantizer.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input"],
        output_names=["output"]
    )
    
    # ONNX推理
    session = ort.InferenceSession("simple_quantizer.onnx")
    onnx_output = session.run(None, {"input": test_input.numpy()})[0]
    
    print(f"ONNX输出: {onnx_output}")
    print(f"差异: {np.abs(pytorch_output.numpy() - onnx_output).max()}")
    
    return np.allclose(pytorch_output.numpy(), onnx_output, atol=1e-6)


def test_real_quantizer():
    print("\n测试真实量化器...")
    
    from models.quantization import DENSEQuantizer
    
    # 创建量化器
    quantizer = DENSEQuantizer(feature_dim=4, code_dim=4)
    quantizer.eval()
    
    # 创建包装模型
    class QuantizerWrapper(torch.nn.Module):
        def __init__(self, quantizer):
            super().__init__()
            self.quantizer = quantizer
            
        def forward(self, x):
            return self.quantizer.quantize_features_to_bits(x)
    
    model = QuantizerWrapper(quantizer)
    
    # 创建测试数据
    test_input = torch.randn(2, 3, 4)
    
    # PyTorch推理
    with torch.no_grad():
        pytorch_output = model(test_input)
    
    print(f"输入形状: {test_input.shape}")
    print(f"PyTorch输出形状: {pytorch_output.shape}")
    print(f"PyTorch输出范围: [{pytorch_output.min():.4f}, {pytorch_output.max():.4f}]")
    
    # 导出ONNX
    torch.onnx.export(
        model,
        test_input,
        "real_quantizer.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input"],
        output_names=["output"]
    )
    
    # ONNX推理
    session = ort.InferenceSession("real_quantizer.onnx")
    onnx_output = session.run(None, {"input": test_input.numpy()})[0]
    
    print(f"ONNX输出形状: {onnx_output.shape}")
    print(f"ONNX输出范围: [{onnx_output.min():.4f}, {onnx_output.max():.4f}]")
    
    diff = np.abs(pytorch_output.numpy() - onnx_output).max()
    print(f"最大差异: {diff}")
    
    return diff < 1e-5


if __name__ == "__main__":
    print("=" * 60)
    print("ONNX Sign函数测试")
    print("=" * 60)
    
    # 测试简单sign函数
    sign_ok = test_sign_onnx()
    
    # 测试简单量化器
    simple_quantizer_ok = test_quantizer_onnx()
    
    # 测试真实量化器
    real_quantizer_ok = test_real_quantizer()
    
    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"简单Sign函数: {'✅' if sign_ok else '❌'}")
    print(f"简单量化器: {'✅' if simple_quantizer_ok else '❌'}")
    print(f"真实量化器: {'✅' if real_quantizer_ok else '❌'}")
    print("=" * 60)
