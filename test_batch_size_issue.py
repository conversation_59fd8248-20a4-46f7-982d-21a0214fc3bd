import argparse
import numpy as np
import onnxruntime as ort
import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


def test_batch_size(batch_size):
    print(f"\n{'='*60}")
    print(f"测试批次大小: {batch_size}")
    print(f"{'='*60}")
    
    # 解析参数
    parser = argparse.ArgumentParser()
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=3, choices=[1, 2, 3])
    parser.add_argument("--trainset", type=str, default="celeba", choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"])
    parser.add_argument("--testset", type=str, default="kodak", choices=["kodak", "CLIC21"])
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)
    parser.add_argument("--model", type=str, default="WITT_W/O", choices=["WITT_W/O"])
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)
    parser.add_argument("--channel_type", type=str, default="rayleigh", choices=["awgn", "rayleigh", "none"])
    parser.add_argument("--multiple_snr", type=str, default="5")
    parser.add_argument("--distortion_metric", type=str, default="MSE", choices=["MSE", "MS-SSIM"])
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)
    args = parser.parse_args()
    
    # 初始化配置
    cfg = BaseConfig(args, training_phase=3)
    
    # 创建模型
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
    quantizer = DENSEQuantizer(feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)
    
    # 加载检查点
    checkpoint = torch.load("F:/szy/jiaozhi/results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth")
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])
    
    net1.eval()
    net2.eval()
    quantizer.eval()
    
    # 使用固定的随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 创建测试数据
    input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    print(f"输入数据形状: {input1.shape}")
    
    # PyTorch推理
    with torch.no_grad():
        feature1 = net1.encode(input1)
        feature2 = net2.encode(input2)
        feature = feature1 + feature2
        B1, L1_feat, D1_feat = feature1.shape
        
        quantized_feature = quantizer.quantize_features_to_bits(
            feature.reshape(B1 * L1_feat, D1_feat)
        )
        quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)
        
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        pytorch_result = interleaved_feature.cpu().detach().numpy()
    
    print(f"PyTorch结果: {pytorch_result.shape}, 范围: [{pytorch_result.min():.4f}, {pytorch_result.max():.4f}]")
    
    # ONNX推理
    try:
        transmitter_session = ort.InferenceSession("transmitter_model_fixed.onnx")
        model_name = "修复后"
    except:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        model_name = "原始"
    
    input1_np = input1.cpu().detach().numpy().astype(np.float32)
    input2_np = input2.cpu().detach().numpy().astype(np.float32)
    
    try:
        transmitter_outputs = transmitter_session.run(
            None, {"input1": input1_np, "input2": input2_np}
        )
        onnx_result = transmitter_outputs[0]
        
        print(f"ONNX结果({model_name}): {onnx_result.shape}, 范围: [{onnx_result.min():.4f}, {onnx_result.max():.4f}]")
        
        # 计算差异
        diff = np.abs(pytorch_result - onnx_result)
        max_diff = diff.max()
        mean_diff = diff.mean()
        
        print(f"差异统计: 最大={max_diff:.6f}, 平均={mean_diff:.6f}")
        
        # 检查前几个元素
        print("前5个元素对比:")
        for i in range(min(5, pytorch_result.shape[1])):
            pt_val = pytorch_result[0, i]
            onnx_val = onnx_result[0, i]
            print(f"  [{i}]: PyTorch={pt_val:.6f}, ONNX={onnx_val:.6f}, 差异={abs(pt_val-onnx_val):.6f}")
        
        return max_diff < 1e-5
        
    except Exception as e:
        print(f"ONNX推理失败: {e}")
        return False


def main():
    print("=" * 80)
    print("批次大小影响测试")
    print("=" * 80)
    
    # 测试不同的批次大小
    batch_sizes = [1, 2, 4, 8, 16, 32]
    results = {}
    
    for batch_size in batch_sizes:
        try:
            success = test_batch_size(batch_size)
            results[batch_size] = success
        except Exception as e:
            print(f"批次大小 {batch_size} 测试失败: {e}")
            results[batch_size] = False
    
    print(f"\n{'='*80}")
    print("测试结果汇总")
    print(f"{'='*80}")
    
    for batch_size, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"批次大小 {batch_size:2d}: {status}")
    
    # 分析结果
    successful_batches = [bs for bs, success in results.items() if success]
    failed_batches = [bs for bs, success in results.items() if not success]
    
    if successful_batches:
        print(f"\n成功的批次大小: {successful_batches}")
    if failed_batches:
        print(f"失败的批次大小: {failed_batches}")
        
    if len(successful_batches) == 1 and successful_batches[0] == 1:
        print("\n🔍 结论: 只有批次大小=1时结果一致，这表明ONNX模型的动态批次支持有问题")
    elif len(successful_batches) == len(batch_sizes):
        print("\n✅ 结论: 所有批次大小都工作正常")
    else:
        print("\n⚠️  结论: 部分批次大小有问题，需要进一步调查")


if __name__ == "__main__":
    main()
