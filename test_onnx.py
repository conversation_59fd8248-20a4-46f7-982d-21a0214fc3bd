import argparse

import numpy as np
import onnxruntime as ort
import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


def main():
    # 读取pytorch模型测试
    cfg = BaseConfig(args, training_phase=3)
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
    quantizer = DENSEQuantizer(feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim).to(
        cfg.device
    )
    # 创建独立的交织器实例
    interleaver = BlockInterleaver().to(cfg.device)
    checkpoint = torch.load(
        "F:/szy/jiaozhi/results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth"
    )
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])

    net1.eval()
    net2.eval()
    quantizer.eval()

    img_h = img_w = 64
    transform = transforms.Compose(
        [
            transforms.Resize((img_h, img_w)),
            transforms.ToTensor(),
        ]
    )
    train_dataset = datasets.ImageFolder(
        root="D:/data/CelebA64/test", transform=transform
    )

    train_loader_u1 = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=True,
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )
    train_loader_u2 = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=True,
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )
    input1, _ = next(iter(train_loader_u1))
    input2, _ = next(iter(train_loader_u2))
    input1 = input1.to(cfg.device)
    input2 = input2.to(cfg.device)

    feature1 = net1.encode(input1)
    feature2 = net2.encode(input2)
    feature = feature1 + feature2
    B1, L1_feat, D1_feat = feature1.shape
    B2, L2_feat, D2_feat = feature2.shape
    quantized_feature = quantizer.quantize_features_to_bits(
        feature.reshape(B1 * L1_feat, D1_feat)
    )
    quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)
    feature_for_channel, interleaver_metadata = interleaver.interleave(
        quantized_feature
    )
    noisy_output_path1 = interleaver.deinterleave(
        feature_for_channel, metadata=interleaver_metadata
    )
    noisy_output_path2 = interleaver.deinterleave(
        feature_for_channel, metadata=interleaver_metadata
    )
    noisy_sum_bits_p1 = noisy_output_path1.reshape(B1 * L1_feat, cfg.quant_code_dim)
    noisy_sum_bits_p2 = noisy_output_path2.reshape(B2 * L2_feat, cfg.quant_code_dim)
    final_features_for_dec1 = quantizer.dequantize_bits_to_features(
        noisy_sum_bits_p1
    ).reshape(B1, L1_feat, D1_feat)
    final_features_for_dec2 = quantizer.dequantize_bits_to_features(
        noisy_sum_bits_p2
    ).reshape(B2, L2_feat, D2_feat)
    recon_image_u1 = net1.decode(final_features_for_dec1)
    recon_image_u2 = net2.decode(final_features_for_dec2)
    criterion = torch.nn.MSELoss()
    print(criterion(recon_image_u1, input1).item())
    print(criterion(recon_image_u2, input2).item())

    psnr1 = net1.calculate_psnr(input1, recon_image_u1)
    psnr2 = net2.calculate_psnr(input2, recon_image_u2)
    print(psnr1.item())
    print(psnr2.item())

    # 测试onnx 模型
    transmitter_seesion = ort.InferenceSession("transmitter_model.onnx")
    receiver_seesion = ort.InferenceSession("receiver_model.onnx")

    # input1 = torch.rand((32, 3, 64, 64))
    # input2 = torch.rand((32, 3, 64, 64))

    input1 = input1.cpu().detach().numpy().astype(np.float32)
    input2 = input2.cpu().detach().numpy().astype(np.float32)

    transmitter_outputs = transmitter_seesion.run(
        None, {"input1": input1, "input2": input2}
    )

    interleaved_features = transmitter_outputs[0]

    receiver_outputs = receiver_seesion.run(
        None, {"interleaved_feature": interleaved_features}
    )

    output1, output2 = receiver_outputs

    mse1 = np.mean((input1 - output1) ** 2)
    mse2 = np.mean((input2 - output2) ** 2)
    print(mse1)
    print(mse2)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Semantic Communication System Training"
    )
    # 实验相关参数
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=3, choices=[1, 2, 3])
    parser.add_argument(
        "--run_all_phases", action="store_true", help="运行所有三个阶段的训练"
    )
    parser.add_argument("--test_only", action="store_true")

    # 数据集相关参数
    parser.add_argument(
        "--trainset",
        type=str,
        default="celeba",
        choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"],
    )
    parser.add_argument(
        "--testset", type=str, default="kodak", choices=["kodak", "CLIC21"]
    )
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)

    # 模型相关参数
    parser.add_argument("--model", type=str, default="WITT_W/O", choices=["WITT_W/O"])
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)

    # 训练相关参数
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)

    # 通道相关参数
    parser.add_argument(
        "--channel_type",
        type=str,
        default="rayleigh",
        choices=["awgn", "rayleigh", "none"],
    )
    parser.add_argument("--multiple_snr", type=str, default="5")

    # 损失函数相关参数
    parser.add_argument(
        "--distortion_metric", type=str, default="MSE", choices=["MSE", "MS-SSIM"]
    )

    # 模型加载相关参数
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)

    args = parser.parse_args()
    main()
