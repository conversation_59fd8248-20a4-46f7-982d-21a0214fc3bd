import argparse

import numpy as np
import onnxruntime as ort
import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


def main():
    print("=" * 80)
    print("PyTorch vs ONNX 推理结果对比分析")
    print("=" * 80)

    # 读取pytorch模型测试
    cfg = BaseConfig(args, training_phase=3)
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
    quantizer = DENSEQuantizer(feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim).to(
        cfg.device
    )
    # 创建独立的交织器实例
    interleaver = BlockInterleaver().to(cfg.device)
    checkpoint = torch.load(
        "F:/szy/jiaozhi/results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth"
    )
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])

    net1.eval()
    net2.eval()
    quantizer.eval()

    img_h = img_w = 64
    transform = transforms.Compose(
        [
            transforms.Resize((img_h, img_w)),
            transforms.ToTensor(),
        ]
    )
    train_dataset = datasets.ImageFolder(
        root="D:/data/CelebA64/test", transform=transform
    )

    # 使用固定的随机种子确保数据一致性
    torch.manual_seed(42)
    np.random.seed(42)

    train_loader_u1 = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=False,  # 改为False确保数据一致性
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )
    train_loader_u2 = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=False,  # 改为False确保数据一致性
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )
    input1, _ = next(iter(train_loader_u1))
    input2, _ = next(iter(train_loader_u2))
    input1 = input1.to(cfg.device)
    input2 = input2.to(cfg.device)

    print(f"\n输入数据形状: input1={input1.shape}, input2={input2.shape}")
    print(
        f"输入数据范围: input1=[{input1.min():.4f}, {input1.max():.4f}], input2=[{input2.min():.4f}, {input2.max():.4f}]"
    )

    # ========== PyTorch 推理 ==========
    print("\n" + "=" * 50)
    print("PyTorch 模型推理")
    print("=" * 50)

    with torch.no_grad():
        # 编码
        feature1 = net1.encode(input1)
        feature2 = net2.encode(input2)
        feature = feature1 + feature2
        print(f"编码特征形状: feature1={feature1.shape}, feature2={feature2.shape}")
        print(f"合并特征形状: {feature.shape}")
        print(f"特征范围: [{feature.min():.4f}, {feature.max():.4f}]")

        B1, L1_feat, D1_feat = feature1.shape
        B2, L2_feat, D2_feat = feature2.shape

        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(
            feature.reshape(B1 * L1_feat, D1_feat)
        )
        quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)
        print(f"量化特征形状: {quantized_feature.shape}")
        print(
            f"量化特征范围: [{quantized_feature.min():.4f}, {quantized_feature.max():.4f}]"
        )

        # 交织
        feature_for_channel, interleaver_metadata = interleaver.interleave(
            quantized_feature
        )
        print(f"交织特征形状: {feature_for_channel.shape}")
        print(f"交织元数据: {interleaver_metadata}")

        # 解交织 (模拟信道传输，这里直接使用相同数据)
        noisy_output_path1 = interleaver.deinterleave(
            feature_for_channel, metadata=interleaver_metadata
        )
        noisy_output_path2 = interleaver.deinterleave(
            feature_for_channel, metadata=interleaver_metadata
        )
        print(
            f"解交织特征形状: path1={noisy_output_path1.shape}, path2={noisy_output_path2.shape}"
        )

        # 反量化
        noisy_sum_bits_p1 = noisy_output_path1.reshape(B1 * L1_feat, cfg.quant_code_dim)
        noisy_sum_bits_p2 = noisy_output_path2.reshape(B2 * L2_feat, cfg.quant_code_dim)
        final_features_for_dec1 = quantizer.dequantize_bits_to_features(
            noisy_sum_bits_p1
        ).reshape(B1, L1_feat, D1_feat)
        final_features_for_dec2 = quantizer.dequantize_bits_to_features(
            noisy_sum_bits_p2
        ).reshape(B2, L2_feat, D2_feat)
        print(
            f"反量化特征形状: dec1={final_features_for_dec1.shape}, dec2={final_features_for_dec2.shape}"
        )
        print(
            f"反量化特征范围: dec1=[{final_features_for_dec1.min():.4f}, {final_features_for_dec1.max():.4f}]"
        )

        # 解码
        recon_image_u1 = net1.decode(final_features_for_dec1)
        recon_image_u2 = net2.decode(final_features_for_dec2)
        print(f"重建图像形状: u1={recon_image_u1.shape}, u2={recon_image_u2.shape}")
        print(
            f"重建图像范围: u1=[{recon_image_u1.min():.4f}, {recon_image_u1.max():.4f}]"
        )

    criterion = torch.nn.MSELoss()
    pytorch_mse1 = criterion(recon_image_u1, input1).item()
    pytorch_mse2 = criterion(recon_image_u2, input2).item()

    psnr1 = net1.calculate_psnr(input1, recon_image_u1)
    psnr2 = net2.calculate_psnr(input2, recon_image_u2)

    print("\nPyTorch 结果:")
    print(f"  MSE1: {pytorch_mse1:.6f}")
    print(f"  MSE2: {pytorch_mse2:.6f}")
    print(f"  PSNR1: {psnr1.item():.4f} dB")
    print(f"  PSNR2: {psnr2.item():.4f} dB")

    # ========== ONNX 推理 ==========
    print("\n" + "=" * 50)
    print("ONNX 模型推理")
    print("=" * 50)

    # 测试修复后的onnx 模型
    print("加载修复后的ONNX模型...")
    try:
        transmitter_session = ort.InferenceSession("transmitter_model_fixed.onnx")
        receiver_session = ort.InferenceSession("receiver_model_fixed.onnx")
        print("✅ 修复后的ONNX模型加载成功")
    except Exception as e:
        print(f"❌ 修复后的ONNX模型加载失败: {e}")
        print("尝试使用原始ONNX模型...")
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")

    # 转换为numpy格式
    input1_np = input1.cpu().detach().numpy().astype(np.float32)
    input2_np = input2.cpu().detach().numpy().astype(np.float32)

    print(f"ONNX输入数据形状: input1={input1_np.shape}, input2={input2_np.shape}")
    print(f"ONNX输入数据范围: input1=[{input1_np.min():.4f}, {input1_np.max():.4f}]")

    # 发送端推理 - 使用batch_size=1逐个处理
    print("使用batch_size=1逐个处理以避免动态批次问题...")
    batch_size = input1_np.shape[0]

    interleaved_features_list = []
    for i in range(batch_size):
        single_input1 = input1_np[i : i + 1]  # 保持4D形状
        single_input2 = input2_np[i : i + 1]

        single_transmitter_outputs = transmitter_session.run(
            None, {"input1": single_input1, "input2": single_input2}
        )
        interleaved_features_list.append(single_transmitter_outputs[0])

    # 合并结果
    interleaved_features = np.concatenate(interleaved_features_list, axis=0)
    print(f"ONNX交织特征形状: {interleaved_features.shape}")
    print(
        f"ONNX交织特征范围: [{interleaved_features.min():.4f}, {interleaved_features.max():.4f}]"
    )

    # 接收端推理 - 同样逐个处理
    output1_list = []
    output2_list = []
    for i in range(batch_size):
        single_interleaved = interleaved_features[i : i + 1]

        single_receiver_outputs = receiver_session.run(
            None, {"interleaved_feature": single_interleaved}
        )
        output1_list.append(single_receiver_outputs[0])
        output2_list.append(single_receiver_outputs[1])

    # 合并结果
    output1 = np.concatenate(output1_list, axis=0)
    output2 = np.concatenate(output2_list, axis=0)
    print(f"ONNX重建图像形状: output1={output1.shape}, output2={output2.shape}")
    print(f"ONNX重建图像范围: output1=[{output1.min():.4f}, {output1.max():.4f}]")

    onnx_mse1 = np.mean((input1_np - output1) ** 2)
    onnx_mse2 = np.mean((input2_np - output2) ** 2)

    print("\nONNX 结果:")
    print(f"  MSE1: {onnx_mse1:.6f}")
    print(f"  MSE2: {onnx_mse2:.6f}")

    # ========== 对比分析 ==========
    print("\n" + "=" * 50)
    print("对比分析")
    print("=" * 50)

    print("MSE差异:")
    print(
        f"  MSE1差异: {abs(pytorch_mse1 - onnx_mse1):.6f} ({abs(pytorch_mse1 - onnx_mse1) / pytorch_mse1 * 100:.2f}%)"
    )
    print(
        f"  MSE2差异: {abs(pytorch_mse2 - onnx_mse2):.6f} ({abs(pytorch_mse2 - onnx_mse2) / pytorch_mse2 * 100:.2f}%)"
    )

    # 检查中间结果差异
    print("\n中间特征对比:")
    pytorch_interleaved = feature_for_channel.cpu().detach().numpy()
    interleaved_diff = np.mean(np.abs(pytorch_interleaved - interleaved_features))
    print(f"  交织特征差异: {interleaved_diff:.6f}")

    pytorch_output1 = recon_image_u1.cpu().detach().numpy()
    pytorch_output2 = recon_image_u2.cpu().detach().numpy()
    output_diff1 = np.mean(np.abs(pytorch_output1 - output1))
    output_diff2 = np.mean(np.abs(pytorch_output2 - output2))
    print(f"  输出图像差异: output1={output_diff1:.6f}, output2={output_diff2:.6f}")

    # 判断差异是否显著
    threshold = 1e-5
    if interleaved_diff > threshold:
        print(f"\n⚠️  警告: 交织特征差异较大 ({interleaved_diff:.6f} > {threshold})")
        print("   可能原因: 交织器元数据处理不一致")

    if output_diff1 > threshold or output_diff2 > threshold:
        print("\n⚠️  警告: 输出图像差异较大")
        print("   可能原因: 量化器或解码器处理不一致")

    if (
        interleaved_diff <= threshold
        and output_diff1 <= threshold
        and output_diff2 <= threshold
    ):
        print(f"\n✅ PyTorch和ONNX推理结果基本一致 (差异 < {threshold})")
    else:
        print("\n❌ PyTorch和ONNX推理结果存在显著差异")

        # 详细诊断
        print("\n详细诊断:")
        print("  1. 检查量化器是否正确导出...")
        print("  2. 检查交织器元数据处理...")
        print("  3. 检查模型权重是否正确加载...")
        print("  4. 检查数值精度问题...")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Semantic Communication System Training"
    )
    # 实验相关参数
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=3, choices=[1, 2, 3])
    parser.add_argument(
        "--run_all_phases", action="store_true", help="运行所有三个阶段的训练"
    )
    parser.add_argument("--test_only", action="store_true")

    # 数据集相关参数
    parser.add_argument(
        "--trainset",
        type=str,
        default="celeba",
        choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"],
    )
    parser.add_argument(
        "--testset", type=str, default="kodak", choices=["kodak", "CLIC21"]
    )
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)

    # 模型相关参数
    parser.add_argument("--model", type=str, default="WITT_W/O", choices=["WITT_W/O"])
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)

    # 训练相关参数
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)

    # 通道相关参数
    parser.add_argument(
        "--channel_type",
        type=str,
        default="rayleigh",
        choices=["awgn", "rayleigh", "none"],
    )
    parser.add_argument("--multiple_snr", type=str, default="5")

    # 损失函数相关参数
    parser.add_argument(
        "--distortion_metric", type=str, default="MSE", choices=["MSE", "MS-SSIM"]
    )

    # 模型加载相关参数
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)

    args = parser.parse_args()
    main()
