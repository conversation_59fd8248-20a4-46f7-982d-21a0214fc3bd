import torch
import torch.nn as nn


class ONNXCompatibleSign(torch.autograd.Function):
    """
    ONNX兼容的Sign函数，确保与PyTorch行为一致
    """

    @staticmethod
    def forward(ctx, input_tensor):
        # 完全复制PyTorch的sign行为：
        # sign(x) = 1 if x > 0, -1 if x < 0, 0 if x == 0
        return torch.where(
            input_tensor > 0,
            torch.ones_like(input_tensor),
            torch.where(
                input_tensor < 0,
                -torch.ones_like(input_tensor),
                torch.zeros_like(input_tensor),
            ),
        )

    @staticmethod
    def backward(ctx, grad_output):
        return grad_output.clamp_(-1, 1)


apply_onnx_sign = ONNXCompatibleSign.apply


class ONNXCompatibleQuantizer(nn.Module):
    """
    ONNX兼容的量化器，解决sign函数导出问题
    """

    def __init__(self, feature_dim: int, code_dim: int):
        super(ONNXCompatibleQuantizer, self).__init__()
        self.feature_dim = feature_dim
        self.code_dim = code_dim

        self.feature_to_code_proj = nn.Linear(feature_dim, code_dim)
        self.code_to_feature_proj = nn.Linear(code_dim, feature_dim)

    def quantize_features_to_bits(self, features: torch.Tensor) -> torch.Tensor:
        if features.shape[-1] != self.feature_dim:
            raise ValueError(
                f"Input feature_dim {features.shape[-1]} does not match quantizer.feature_dim {self.feature_dim}"
            )
        pre_quant_code = self.feature_to_code_proj(features)
        # 使用ONNX兼容的sign函数
        bits = apply_onnx_sign(pre_quant_code)
        return bits

    def dequantize_bits_to_features(
        self, bits_after_channel: torch.Tensor
    ) -> torch.Tensor:
        if bits_after_channel.shape[-1] != self.code_dim:
            raise ValueError(
                f"Input bits_after_channel dim {bits_after_channel.shape[-1]} does not match quantizer.code_dim {self.code_dim}"
            )
        # 确保输入是+1/-1
        cleaned_bits = apply_onnx_sign(bits_after_channel)
        reconstructed_features = self.code_to_feature_proj(cleaned_bits)
        return reconstructed_features


def test_sign_compatibility():
    """测试sign函数的兼容性"""
    print("测试Sign函数兼容性...")

    # 创建测试数据，包含边界情况
    test_data = torch.tensor([-2.0, -1.0, -0.5, 0.0, 0.5, 1.0, 2.0])

    # PyTorch原生sign
    pytorch_sign = torch.sign(test_data)

    # ONNX兼容sign
    onnx_sign = apply_onnx_sign(test_data)

    print(f"输入数据: {test_data}")
    print(f"PyTorch sign: {pytorch_sign}")
    print(f"ONNX兼容sign: {onnx_sign}")
    print(f"是否一致: {torch.allclose(pytorch_sign, onnx_sign)}")

    return torch.allclose(pytorch_sign, onnx_sign)


def copy_quantizer_weights(original_quantizer, new_quantizer):
    """复制量化器权重"""
    new_quantizer.feature_to_code_proj.weight.data = (
        original_quantizer.feature_to_code_proj.weight.data.clone()
    )
    new_quantizer.feature_to_code_proj.bias.data = (
        original_quantizer.feature_to_code_proj.bias.data.clone()
    )
    new_quantizer.code_to_feature_proj.weight.data = (
        original_quantizer.code_to_feature_proj.weight.data.clone()
    )
    new_quantizer.code_to_feature_proj.bias.data = (
        original_quantizer.code_to_feature_proj.bias.data.clone()
    )


def test_quantizer_equivalence():
    """测试量化器等价性"""
    print("\n测试量化器等价性...")

    from models.quantization import DENSEQuantizer

    feature_dim = 512
    code_dim = 128
    batch_size = 4
    seq_len = 64

    # 创建原始量化器和ONNX兼容量化器
    original_quantizer = DENSEQuantizer(feature_dim, code_dim)
    onnx_quantizer = ONNXCompatibleQuantizer(feature_dim, code_dim)

    # 复制权重
    copy_quantizer_weights(original_quantizer, onnx_quantizer)

    # 设置为评估模式
    original_quantizer.eval()
    onnx_quantizer.eval()

    # 创建测试数据
    test_features = torch.randn(batch_size, seq_len, feature_dim)

    with torch.no_grad():
        # 原始量化器
        original_bits = original_quantizer.quantize_features_to_bits(test_features)
        original_reconstructed = original_quantizer.dequantize_bits_to_features(
            original_bits
        )

        # ONNX兼容量化器
        onnx_bits = onnx_quantizer.quantize_features_to_bits(test_features)
        onnx_reconstructed = onnx_quantizer.dequantize_bits_to_features(onnx_bits)

    print(f"量化结果一致: {torch.allclose(original_bits, onnx_bits, atol=1e-6)}")
    print(
        f"重建结果一致: {torch.allclose(original_reconstructed, onnx_reconstructed, atol=1e-6)}"
    )

    bits_diff = torch.mean(torch.abs(original_bits - onnx_bits)).item()
    recon_diff = torch.mean(
        torch.abs(original_reconstructed - onnx_reconstructed)
    ).item()

    print(f"量化差异: {bits_diff:.8f}")
    print(f"重建差异: {recon_diff:.8f}")

    return bits_diff < 1e-6 and recon_diff < 1e-6


if __name__ == "__main__":
    print("=" * 60)
    print("ONNX量化器兼容性测试")
    print("=" * 60)

    # 测试sign函数
    sign_ok = test_sign_compatibility()

    # 测试量化器
    quantizer_ok = test_quantizer_equivalence()

    print("\n" + "=" * 60)
    print("测试结果:")
    print(f"Sign函数兼容性: {'✅' if sign_ok else '❌'}")
    print(f"量化器等价性: {'✅' if quantizer_ok else '❌'}")

    if sign_ok and quantizer_ok:
        print("✅ 所有测试通过，可以使用ONNX兼容量化器")
    else:
        print("❌ 存在兼容性问题，需要进一步调试")
    print("=" * 60)
