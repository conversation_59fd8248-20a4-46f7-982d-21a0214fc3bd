#!/usr/bin/env python3
"""
测试更新后的convert_to_onnx.py脚本
"""

import os
import sys
import torch
import tempfile
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


class DummyArgs:
    """模拟命令行参数"""
    experiment_name = "test_exp"
    model = "WITT_W/O"
    multiple_snr = "5"
    distortion_metric = "MSE"
    trainset = "celeba"
    testset = "kodak"
    C = 512
    quant_code_dim = 128
    channel_type = "awgn"
    data_path = "D:/data/CelebA64"
    batch_size = 32
    num_workers = 0
    training_phase = 3
    lr_core = 0.0001
    lr_quantizer = 0.00001
    epochs = 5


def create_dummy_checkpoint():
    """创建一个虚拟的检查点文件用于测试"""
    args = DummyArgs()
    cfg = BaseConfig(args, training_phase=3)
    
    # 创建模型
    net1 = CoreModel(args, cfg)
    net2 = CoreModel(args, cfg)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs["embed_dims"][-1], 
        code_dim=cfg.quant_code_dim
    )
    
    # 创建检查点
    checkpoint = {
        "epoch": 5,
        "phase": 3,
        "args": vars(args),
        "config": vars(cfg),
        "net1_state_dict": net1.state_dict(),
        "net2_state_dict": net2.state_dict(),
        "quantizer_state_dict": quantizer.state_dict(),
        "avg_loss": 0.1234,
    }
    
    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.pth', delete=False)
    torch.save(checkpoint, temp_file.name)
    temp_file.close()
    
    return temp_file.name


def test_convert_to_onnx():
    """测试convert_to_onnx.py的功能"""
    
    print("=" * 60)
    print("测试convert_to_onnx.py脚本")
    print("=" * 60)
    
    # 创建虚拟检查点
    print("1. 创建虚拟检查点...")
    checkpoint_path = create_dummy_checkpoint()
    print(f"✅ 虚拟检查点创建成功: {checkpoint_path}")
    
    try:
        # 导入convert_to_onnx模块
        print("\n2. 导入convert_to_onnx模块...")
        import convert_to_onnx
        print("✅ 模块导入成功")
        
        # 模拟命令行参数
        print("\n3. 设置测试参数...")
        original_argv = sys.argv
        sys.argv = [
            'convert_to_onnx.py',
            '--model_path', checkpoint_path,
            '--trainset', 'celeba',
            '--model', 'WITT_W/O',
            '--C', '512',
            '--quant_code_dim', '128',
            '--experiment_name', 'test_convert'
        ]
        
        # 测试参数解析
        args = convert_to_onnx.parse_args()
        print("✅ 参数解析成功")
        print(f"   模型路径: {args.model_path}")
        print(f"   模型类型: {args.model}")
        print(f"   训练集: {args.trainset}")
        
        # 测试配置创建
        print("\n4. 测试配置创建...")
        cfg = BaseConfig(args, training_phase=3)
        print("✅ 配置创建成功")
        print(f"   设备: {cfg.device}")
        print(f"   图像尺寸: {cfg.image_dims}")
        
        # 测试模型创建
        print("\n5. 测试模型创建...")
        net1 = CoreModel(args, cfg).to(cfg.device)
        net2 = CoreModel(args, cfg).to(cfg.device)
        quantizer = DENSEQuantizer(
            feature_dim=cfg.encoder_kwargs["embed_dims"][-1], 
            code_dim=cfg.quant_code_dim
        ).to(cfg.device)
        interleaver = BlockInterleaver().to(cfg.device)
        print("✅ 模型创建成功")
        
        # 测试检查点加载
        print("\n6. 测试检查点加载...")
        checkpoint = torch.load(checkpoint_path, map_location=cfg.device)
        print("✅ 检查点加载成功")
        print(f"   检查点键: {list(checkpoint.keys())}")
        
        # 测试前向传播
        print("\n7. 测试前向传播...")
        batch_size = 1
        dummy_input1 = torch.randn(batch_size, 3, cfg.image_dims[1], cfg.image_dims[2]).to(cfg.device)
        dummy_input2 = torch.randn(batch_size, 3, cfg.image_dims[1], cfg.image_dims[2]).to(cfg.device)
        
        with torch.no_grad():
            # 编码
            feature1 = net1.encode(dummy_input1)
            feature2 = net2.encode(dummy_input2)
            feature = feature1 + feature2
            print(f"   编码特征形状: {feature.shape}")
            
            # 量化
            B1, L1_feat, D1_feat = feature1.shape
            quantized_feature = quantizer.quantize_features_to_bits(
                feature.reshape(B1 * L1_feat, D1_feat)
            )
            quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)
            print(f"   量化特征形状: {quantized_feature.shape}")
            
            # 交织
            interleaved_feature, metadata = interleaver.interleave(quantized_feature)
            print(f"   交织特征形状: {interleaved_feature.shape}")
            print(f"   元数据: {metadata}")
            
            # 解交织
            deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
            print(f"   解交织特征形状: {deinterleaved_feature.shape}")
            
            # 检查是否能正确恢复
            if torch.allclose(quantized_feature, deinterleaved_feature, atol=1e-6):
                print("✅ 交织/解交织功能正常")
            else:
                print("❌ 交织/解交织功能异常")
                return False
        
        print("\n✅ 所有测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始命令行参数
        sys.argv = original_argv
        
        # 清理临时文件
        try:
            os.unlink(checkpoint_path)
            print(f"\n🧹 清理临时文件: {checkpoint_path}")
        except:
            pass


if __name__ == "__main__":
    success = test_convert_to_onnx()
    if not success:
        exit(1)
    
    print("\n" + "=" * 60)
    print("✅ convert_to_onnx.py 测试完成!")
    print("脚本已适应当前的代码架构")
    print("=" * 60)
