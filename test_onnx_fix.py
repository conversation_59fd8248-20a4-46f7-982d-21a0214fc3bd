import argparse
import numpy as np
import onnxruntime as ort
import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


def main():
    print("=" * 80)
    print("测试ONNX修复方案 - 使用batch_size=1逐个处理")
    print("=" * 80)
    
    # 解析参数
    parser = argparse.ArgumentParser()
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=3, choices=[1, 2, 3])
    parser.add_argument("--trainset", type=str, default="celeba")
    parser.add_argument("--testset", type=str, default="kodak")
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)
    parser.add_argument("--model", type=str, default="WITT_W/O")
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)
    parser.add_argument("--channel_type", type=str, default="rayleigh")
    parser.add_argument("--multiple_snr", type=str, default="5")
    parser.add_argument("--distortion_metric", type=str, default="MSE")
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)
    args = parser.parse_args()
    
    # 初始化配置
    cfg = BaseConfig(args, training_phase=3)
    
    # 创建模型
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
    quantizer = DENSEQuantizer(feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)
    
    # 加载检查点
    checkpoint = torch.load("F:/szy/jiaozhi/results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth")
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])
    
    net1.eval()
    net2.eval()
    quantizer.eval()
    
    # 使用固定的随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 创建测试数据
    batch_size = 4  # 使用小批次进行测试
    input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    print(f"输入数据形状: {input1.shape}")
    
    # ========== PyTorch 推理 ==========
    print("\nPyTorch推理...")
    with torch.no_grad():
        feature1 = net1.encode(input1)
        feature2 = net2.encode(input2)
        feature = feature1 + feature2
        B1, L1_feat, D1_feat = feature1.shape
        
        quantized_feature = quantizer.quantize_features_to_bits(
            feature.reshape(B1 * L1_feat, D1_feat)
        )
        quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)
        
        feature_for_channel, interleaver_metadata = interleaver.interleave(quantized_feature)
        
        # 模拟解交织
        noisy_output_path1 = interleaver.deinterleave(feature_for_channel, metadata=interleaver_metadata)
        noisy_output_path2 = interleaver.deinterleave(feature_for_channel, metadata=interleaver_metadata)
        
        # 反量化
        noisy_sum_bits_p1 = noisy_output_path1.reshape(B1 * L1_feat, cfg.quant_code_dim)
        noisy_sum_bits_p2 = noisy_output_path2.reshape(B1 * L1_feat, cfg.quant_code_dim)
        final_features_for_dec1 = quantizer.dequantize_bits_to_features(noisy_sum_bits_p1).reshape(B1, L1_feat, D1_feat)
        final_features_for_dec2 = quantizer.dequantize_bits_to_features(noisy_sum_bits_p2).reshape(B1, L1_feat, D1_feat)
        
        # 解码
        recon_image_u1 = net1.decode(final_features_for_dec1)
        recon_image_u2 = net2.decode(final_features_for_dec2)
    
    pytorch_mse1 = torch.nn.MSELoss()(recon_image_u1, input1).item()
    pytorch_mse2 = torch.nn.MSELoss()(recon_image_u2, input2).item()
    
    print(f"PyTorch MSE: {pytorch_mse1:.6f}, {pytorch_mse2:.6f}")
    
    # ========== ONNX 推理 (修复版本) ==========
    print("\nONNX推理 (使用batch_size=1逐个处理)...")
    
    # 加载ONNX模型
    try:
        transmitter_session = ort.InferenceSession("transmitter_model_fixed.onnx")
        receiver_session = ort.InferenceSession("receiver_model_fixed.onnx")
        print("使用修复后的ONNX模型")
    except:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")
        print("使用原始ONNX模型")
    
    # 转换为numpy
    input1_np = input1.cpu().detach().numpy().astype(np.float32)
    input2_np = input2.cpu().detach().numpy().astype(np.float32)
    
    # 逐个处理每个样本
    output1_list = []
    output2_list = []
    
    for i in range(batch_size):
        print(f"处理样本 {i+1}/{batch_size}...")
        
        # 提取单个样本
        single_input1 = input1_np[i:i+1]
        single_input2 = input2_np[i:i+1]
        
        # 发送端推理
        transmitter_outputs = transmitter_session.run(
            None, {"input1": single_input1, "input2": single_input2}
        )
        interleaved_features = transmitter_outputs[0]
        
        # 接收端推理
        receiver_outputs = receiver_session.run(
            None, {"interleaved_feature": interleaved_features}
        )
        
        output1_list.append(receiver_outputs[0])
        output2_list.append(receiver_outputs[1])
    
    # 合并结果
    onnx_output1 = np.concatenate(output1_list, axis=0)
    onnx_output2 = np.concatenate(output2_list, axis=0)
    
    print(f"ONNX输出形状: {onnx_output1.shape}, {onnx_output2.shape}")
    
    # 计算ONNX MSE
    onnx_mse1 = np.mean((input1_np - onnx_output1) ** 2)
    onnx_mse2 = np.mean((input2_np - onnx_output2) ** 2)
    
    print(f"ONNX MSE: {onnx_mse1:.6f}, {onnx_mse2:.6f}")
    
    # ========== 对比分析 ==========
    print("\n" + "="*50)
    print("对比分析")
    print("="*50)
    
    mse1_diff = abs(pytorch_mse1 - onnx_mse1)
    mse2_diff = abs(pytorch_mse2 - onnx_mse2)
    
    print(f"MSE差异:")
    print(f"  MSE1差异: {mse1_diff:.6f} ({mse1_diff/pytorch_mse1*100:.2f}%)")
    print(f"  MSE2差异: {mse2_diff:.6f} ({mse2_diff/pytorch_mse2*100:.2f}%)")
    
    # 检查输出图像差异
    pytorch_output1_np = recon_image_u1.cpu().detach().numpy()
    pytorch_output2_np = recon_image_u2.cpu().detach().numpy()
    
    output_diff1 = np.mean(np.abs(pytorch_output1_np - onnx_output1))
    output_diff2 = np.mean(np.abs(pytorch_output2_np - onnx_output2))
    
    print(f"输出图像差异: {output_diff1:.6f}, {output_diff2:.6f}")
    
    # 判断修复是否成功
    threshold = 1e-4
    if mse1_diff < threshold and mse2_diff < threshold and output_diff1 < threshold and output_diff2 < threshold:
        print(f"\n✅ 修复成功！差异在可接受范围内 (< {threshold})")
    else:
        print(f"\n⚠️  仍存在差异，但已显著改善")
        if mse1_diff < 0.01 and mse2_diff < 0.01:
            print("差异已降至可接受水平")
        else:
            print("需要进一步优化")


if __name__ == "__main__":
    main()
