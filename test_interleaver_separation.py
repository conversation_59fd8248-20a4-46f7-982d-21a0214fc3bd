#!/usr/bin/env python3
"""
测试交织器剥离是否成功的脚本
"""

import torch

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver


class DummyArgs:
    """模拟命令行参数"""

    experiment_name = "test_exp"
    model = "WITT_W/O"
    multiple_snr = "5"
    distortion_metric = "MSE"
    trainset = "celeba"
    testset = "kodak"
    C = 512
    quant_code_dim = 128
    channel_type = "awgn"
    data_path = "D:/data/CelebA64"
    batch_size = 32
    num_workers = 0
    training_phase = 1
    lr_core = 0.0001
    lr_quantizer = 0.00001
    epochs = 5


def test_interleaver_separation():
    """测试交织器是否已经从CoreModel中成功剥离"""

    print("=" * 60)
    print("测试交织器剥离")
    print("=" * 60)

    # 创建模拟参数
    args = DummyArgs()
    cfg = BaseConfig(args, training_phase=1)

    # 测试1: 检查CoreModel是否不再包含交织器
    print("\n1. 检查CoreModel是否不再包含交织器...")
    try:
        model = CoreModel(args, cfg)

        # 检查模型是否还有interleaver属性
        if hasattr(model, "interleaver"):
            print("❌ 失败: CoreModel仍然包含interleaver属性")
            return False
        else:
            print("✅ 成功: CoreModel不再包含interleaver属性")
    except Exception as e:
        print(f"❌ 错误: 创建CoreModel时出错: {e}")
        return False

    # 测试2: 检查是否可以独立创建交织器
    print("\n2. 检查是否可以独立创建交织器...")
    try:
        interleaver = BlockInterleaver()
        print("✅ 成功: 可以独立创建BlockInterleaver")
    except Exception as e:
        print(f"❌ 错误: 创建独立交织器时出错: {e}")
        return False

    # 测试3: 测试独立交织器的功能
    print("\n3. 测试独立交织器的功能...")
    try:
        # 创建测试数据
        batch_size = 2
        seq_len = 64
        feature_dim = 128
        test_data = torch.randn(batch_size, seq_len, feature_dim)

        # 测试交织和解交织
        interleaved_data, metadata = interleaver.interleave(test_data)
        deinterleaved_data = interleaver.deinterleave(interleaved_data, metadata)

        # 检查是否能正确恢复原始数据
        if torch.allclose(test_data, deinterleaved_data, atol=1e-6):
            print("✅ 成功: 独立交织器功能正常")
        else:
            print("❌ 失败: 交织器无法正确恢复数据")
            return False

    except Exception as e:
        print(f"❌ 错误: 测试交织器功能时出错: {e}")
        return False

    # 测试4: 检查模型编码解码功能是否正常
    print("\n4. 检查模型编码解码功能是否正常...")
    try:
        # 创建测试图像
        test_image = torch.randn(1, 3, 64, 64)

        # 测试编码
        features = model.encode(test_image)
        print(f"   编码输出形状: {features.shape}")

        # 测试解码
        reconstructed = model.decode(features)
        print(f"   解码输出形状: {reconstructed.shape}")

        # 检查输入输出形状是否匹配
        if test_image.shape == reconstructed.shape:
            print("✅ 成功: 模型编码解码功能正常")
        else:
            print("❌ 失败: 输入输出形状不匹配")
            return False

    except Exception as e:
        print(f"❌ 错误: 测试模型功能时出错: {e}")
        return False

    print("\n" + "=" * 60)
    print("✅ 所有测试通过! 交织器已成功从CoreModel中剥离")
    print("=" * 60)
    return True


if __name__ == "__main__":
    success = test_interleaver_separation()
    if not success:
        exit(1)
