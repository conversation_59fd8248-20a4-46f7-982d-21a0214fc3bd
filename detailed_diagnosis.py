import argparse
import numpy as np
import onnxruntime as ort
import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


def main():
    print("=" * 80)
    print("详细诊断 - 逐步检查每个组件")
    print("=" * 80)
    
    # 解析参数
    parser = argparse.ArgumentParser()
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=3, choices=[1, 2, 3])
    parser.add_argument("--trainset", type=str, default="celeba", choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"])
    parser.add_argument("--testset", type=str, default="kodak", choices=["kodak", "CLIC21"])
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)
    parser.add_argument("--model", type=str, default="WITT_W/O", choices=["WITT_W/O"])
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)
    parser.add_argument("--channel_type", type=str, default="rayleigh", choices=["awgn", "rayleigh", "none"])
    parser.add_argument("--multiple_snr", type=str, default="5")
    parser.add_argument("--distortion_metric", type=str, default="MSE", choices=["MSE", "MS-SSIM"])
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)
    args = parser.parse_args()
    
    # 初始化配置
    cfg = BaseConfig(args, training_phase=3)
    
    # 创建模型
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
    quantizer = DENSEQuantizer(feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)
    
    # 加载检查点
    checkpoint = torch.load("F:/szy/jiaozhi/results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth")
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])
    
    net1.eval()
    net2.eval()
    quantizer.eval()
    
    # 准备测试数据 - 使用简单的固定数据
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 使用简单的测试数据
    batch_size = 2  # 使用小批次便于调试
    input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    print(f"输入数据形状: {input1.shape}")
    print(f"输入数据统计: mean={input1.mean():.6f}, std={input1.std():.6f}")
    
    # ========== 步骤1: 编码 ==========
    print("\n" + "="*50)
    print("步骤1: 编码")
    print("="*50)
    
    with torch.no_grad():
        feature1 = net1.encode(input1)
        feature2 = net2.encode(input2)
        feature = feature1 + feature2
        
        print(f"feature1: {feature1.shape}, 范围: [{feature1.min():.4f}, {feature1.max():.4f}]")
        print(f"feature2: {feature2.shape}, 范围: [{feature2.min():.4f}, {feature2.max():.4f}]")
        print(f"合并特征: {feature.shape}, 范围: [{feature.min():.4f}, {feature.max():.4f}]")
        
        B1, L1_feat, D1_feat = feature1.shape
    
    # ========== 步骤2: 量化 ==========
    print("\n" + "="*50)
    print("步骤2: 量化")
    print("="*50)
    
    with torch.no_grad():
        # 重新整形
        feature_reshaped = feature.reshape(B1 * L1_feat, D1_feat)
        print(f"重新整形后: {feature_reshaped.shape}")
        
        # 量化前的线性变换
        pre_quant = quantizer.feature_to_code_proj(feature_reshaped)
        print(f"量化前线性变换: {pre_quant.shape}, 范围: [{pre_quant.min():.4f}, {pre_quant.max():.4f}]")
        
        # 应用sign函数
        quantized_bits = torch.sign(pre_quant)
        print(f"量化后: {quantized_bits.shape}, 范围: [{quantized_bits.min():.4f}, {quantized_bits.max():.4f}]")
        print(f"量化后唯一值: {torch.unique(quantized_bits)}")
        
        # 使用量化器的方法
        quantized_feature_method = quantizer.quantize_features_to_bits(feature_reshaped)
        print(f"量化器方法结果一致: {torch.allclose(quantized_bits, quantized_feature_method)}")
        
        quantized_feature = quantized_feature_method.reshape(B1, L1_feat, cfg.quant_code_dim)
        print(f"重新整形后量化特征: {quantized_feature.shape}")
    
    # ========== 步骤3: 交织 ==========
    print("\n" + "="*50)
    print("步骤3: 交织")
    print("="*50)
    
    with torch.no_grad():
        # 手动执行交织步骤
        print(f"交织前: {quantized_feature.shape}")
        
        # 交换维度
        x_interleaved = quantized_feature.permute(1, 0, 2)  # [seq_len, batch, feature_dim]
        print(f"交换维度后: {x_interleaved.shape}")
        
        # 重新整形
        x_interleaved_flat = x_interleaved.reshape(batch_size, -1)
        print(f"展平后: {x_interleaved_flat.shape}")
        
        # 使用交织器方法
        interleaved_method, metadata = interleaver.interleave(quantized_feature)
        print(f"交织器方法结果一致: {torch.allclose(x_interleaved_flat, interleaved_method)}")
        print(f"元数据: {metadata}")
        
        pytorch_interleaved = interleaved_method.cpu().detach().numpy()
    
    # ========== 步骤4: ONNX发送端 ==========
    print("\n" + "="*50)
    print("步骤4: ONNX发送端")
    print("="*50)
    
    try:
        transmitter_session = ort.InferenceSession("transmitter_model_fixed.onnx")
        print("使用修复后的发送端模型")
    except:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        print("使用原始发送端模型")
    
    input1_np = input1.cpu().detach().numpy().astype(np.float32)
    input2_np = input2.cpu().detach().numpy().astype(np.float32)
    
    transmitter_outputs = transmitter_session.run(
        None, {"input1": input1_np, "input2": input2_np}
    )
    onnx_interleaved = transmitter_outputs[0]
    
    print(f"ONNX输出: {onnx_interleaved.shape}, 范围: [{onnx_interleaved.min():.4f}, {onnx_interleaved.max():.4f}]")
    
    # ========== 步骤5: 对比分析 ==========
    print("\n" + "="*50)
    print("步骤5: 对比分析")
    print("="*50)
    
    diff = np.abs(pytorch_interleaved - onnx_interleaved)
    print(f"绝对差异统计:")
    print(f"  最大差异: {diff.max():.6f}")
    print(f"  平均差异: {diff.mean():.6f}")
    print(f"  差异标准差: {diff.std():.6f}")
    print(f"  差异>0.1的元素数量: {(diff > 0.1).sum()}")
    print(f"  差异>0.5的元素数量: {(diff > 0.5).sum()}")
    
    # 检查是否有系统性差异
    print(f"\nPyTorch输出统计:")
    print(f"  均值: {pytorch_interleaved.mean():.6f}")
    print(f"  标准差: {pytorch_interleaved.std():.6f}")
    print(f"  最小值: {pytorch_interleaved.min():.6f}")
    print(f"  最大值: {pytorch_interleaved.max():.6f}")
    
    print(f"\nONNX输出统计:")
    print(f"  均值: {onnx_interleaved.mean():.6f}")
    print(f"  标准差: {onnx_interleaved.std():.6f}")
    print(f"  最小值: {onnx_interleaved.min():.6f}")
    print(f"  最大值: {onnx_interleaved.max():.6f}")
    
    # 检查前几个元素的详细差异
    print(f"\n前10个元素对比:")
    for i in range(min(10, pytorch_interleaved.shape[1])):
        pt_val = pytorch_interleaved[0, i]
        onnx_val = onnx_interleaved[0, i]
        print(f"  [{i}]: PyTorch={pt_val:.6f}, ONNX={onnx_val:.6f}, 差异={abs(pt_val-onnx_val):.6f}")
    
    if diff.max() > 1e-5:
        print(f"\n❌ 发现显著差异！最大差异: {diff.max():.6f}")
        print("可能的原因:")
        print("1. 模型权重加载不一致")
        print("2. 数值精度问题")
        print("3. ONNX导出时的操作符转换问题")
        print("4. 批次大小相关的问题")
    else:
        print(f"\n✅ 差异在可接受范围内 (< 1e-5)")


if __name__ == "__main__":
    main()
