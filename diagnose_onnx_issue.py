import argparse

import numpy as np
import onnxruntime as ort
import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


def main():
    print("=" * 80)
    print("ONNX 问题诊断")
    print("=" * 80)

    # 解析参数
    parser = argparse.ArgumentParser()
    parser.add_argument("--experiment_name", type=str, default="default_exp")
    parser.add_argument("--training_phase", type=int, default=3, choices=[1, 2, 3])
    parser.add_argument(
        "--trainset",
        type=str,
        default="celeba",
        choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet", "celeba256"],
    )
    parser.add_argument(
        "--testset", type=str, default="kodak", choices=["kodak", "CLIC21"]
    )
    parser.add_argument("--data_path", type=str, default="D:/data/CelebA64")
    parser.add_argument("--batch_size", type=int, default=None)
    parser.add_argument("--num_workers", type=int, default=0)
    parser.add_argument("--model", type=str, default="WITT_W/O", choices=["WITT_W/O"])
    parser.add_argument("--C", type=int, default=512)
    parser.add_argument("--quant_code_dim", type=int, default=128)
    parser.add_argument("--lr_core", type=float, default=0.0001)
    parser.add_argument("--lr_quantizer", type=float, default=0.00001)
    parser.add_argument("--epochs", type=int, default=5)
    parser.add_argument(
        "--channel_type",
        type=str,
        default="rayleigh",
        choices=["awgn", "rayleigh", "none"],
    )
    parser.add_argument("--multiple_snr", type=str, default="5")
    parser.add_argument(
        "--distortion_metric", type=str, default="MSE", choices=["MSE", "MS-SSIM"]
    )
    parser.add_argument("--resume_checkpoint", type=str, default=None)
    parser.add_argument("--load_pretrained_core", type=str, default=None)
    args = parser.parse_args()

    # 初始化配置
    cfg = BaseConfig(args, training_phase=3)

    # 创建模型
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    q_feat_dim = cfg.encoder_kwargs["embed_dims"][-1]
    quantizer = DENSEQuantizer(feature_dim=q_feat_dim, code_dim=cfg.quant_code_dim).to(
        cfg.device
    )
    interleaver = BlockInterleaver().to(cfg.device)

    # 加载检查点
    checkpoint = torch.load(
        "F:/szy/jiaozhi/results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth"
    )
    net1.load_state_dict(checkpoint["net1_state_dict"])
    net2.load_state_dict(checkpoint["net2_state_dict"])
    quantizer.load_state_dict(checkpoint["quantizer_state_dict"])

    net1.eval()
    net2.eval()
    quantizer.eval()

    # 准备测试数据
    torch.manual_seed(42)
    np.random.seed(42)

    img_h = img_w = 64
    transform = transforms.Compose(
        [
            transforms.Resize((img_h, img_w)),
            transforms.ToTensor(),
        ]
    )
    train_dataset = datasets.ImageFolder(
        root="D:/data/CelebA64/test", transform=transform
    )

    train_loader = DataLoader(
        train_dataset,
        batch_size=cfg.batch_size,
        shuffle=False,
        num_workers=cfg.num_workers,
        pin_memory=True,
        drop_last=True,
    )

    input1, _ = next(iter(train_loader))
    input2, _ = next(iter(train_loader))
    input1 = input1.to(cfg.device)
    input2 = input2.to(cfg.device)

    print(f"输入数据形状: {input1.shape}")

    # ========== 步骤1: PyTorch 发送端处理 ==========
    print("\n" + "=" * 50)
    print("步骤1: PyTorch 发送端处理")
    print("=" * 50)

    with torch.no_grad():
        # 编码
        feature1 = net1.encode(input1)
        feature2 = net2.encode(input2)
        feature = feature1 + feature2
        print(
            f"编码特征: {feature.shape}, 范围: [{feature.min():.4f}, {feature.max():.4f}]"
        )

        B1, L1_feat, D1_feat = feature1.shape

        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(
            feature.reshape(B1 * L1_feat, D1_feat)
        )
        quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)
        print(
            f"量化特征: {quantized_feature.shape}, 范围: [{quantized_feature.min():.4f}, {quantized_feature.max():.4f}]"
        )

        # 交织
        feature_for_channel, interleaver_metadata = interleaver.interleave(
            quantized_feature
        )
        print(f"交织特征: {feature_for_channel.shape}")
        print(f"交织元数据: {interleaver_metadata}")

        # 保存PyTorch结果用于对比
        pytorch_interleaved = feature_for_channel.cpu().detach().numpy()

    # ========== 步骤2: ONNX 发送端处理 ==========
    print("\n" + "=" * 50)
    print("步骤2: ONNX 发送端处理")
    print("=" * 50)

    try:
        transmitter_session = ort.InferenceSession("transmitter_model_fixed.onnx")
        print("使用修复后的发送端模型")
    except:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        print("使用原始发送端模型")

    input1_np = input1.cpu().detach().numpy().astype(np.float32)
    input2_np = input2.cpu().detach().numpy().astype(np.float32)

    transmitter_outputs = transmitter_session.run(
        None, {"input1": input1_np, "input2": input2_np}
    )
    onnx_interleaved = transmitter_outputs[0]
    print(
        f"ONNX交织特征: {onnx_interleaved.shape}, 范围: [{onnx_interleaved.min():.4f}, {onnx_interleaved.max():.4f}]"
    )

    # 对比发送端结果
    interleaved_diff = np.mean(np.abs(pytorch_interleaved - onnx_interleaved))
    print(f"发送端差异: {interleaved_diff:.6f}")

    if interleaved_diff > 1e-5:
        print("❌ 发送端就存在差异！")
        return
    else:
        print("✅ 发送端结果一致")

    # ========== 步骤3: PyTorch 接收端处理 ==========
    print("\n" + "=" * 50)
    print("步骤3: PyTorch 接收端处理")
    print("=" * 50)

    with torch.no_grad():
        # 使用相同的交织特征进行解交织
        test_interleaved = torch.from_numpy(onnx_interleaved).to(cfg.device)

        # 解交织
        deinterleaved = interleaver.deinterleave(
            test_interleaved, metadata=interleaver_metadata
        )
        print(
            f"解交织特征: {deinterleaved.shape}, 范围: [{deinterleaved.min():.4f}, {deinterleaved.max():.4f}]"
        )

        # 反量化
        B_new, seq_len_new, feature_dim_new = deinterleaved.shape
        deinterleaved_reshaped = deinterleaved.reshape(
            B_new * seq_len_new, feature_dim_new
        )
        dequantized = quantizer.dequantize_bits_to_features(deinterleaved_reshaped)
        dequantized = dequantized.reshape(B_new, seq_len_new, -1)
        print(
            f"反量化特征: {dequantized.shape}, 范围: [{dequantized.min():.4f}, {dequantized.max():.4f}]"
        )

        # 解码
        recon1_pytorch = net1.decode(dequantized)
        recon2_pytorch = net2.decode(dequantized)
        print(
            f"PyTorch重建: {recon1_pytorch.shape}, 范围: [{recon1_pytorch.min():.4f}, {recon1_pytorch.max():.4f}]"
        )

    # ========== 步骤4: ONNX 接收端处理 ==========
    print("\n" + "=" * 50)
    print("步骤4: ONNX 接收端处理")
    print("=" * 50)

    try:
        receiver_session = ort.InferenceSession("receiver_model_fixed.onnx")
        print("使用修复后的接收端模型")
    except:
        receiver_session = ort.InferenceSession("receiver_model.onnx")
        print("使用原始接收端模型")

    receiver_outputs = receiver_session.run(
        None, {"interleaved_feature": onnx_interleaved}
    )
    recon1_onnx, recon2_onnx = receiver_outputs
    print(
        f"ONNX重建: {recon1_onnx.shape}, 范围: [{recon1_onnx.min():.4f}, {recon1_onnx.max():.4f}]"
    )

    # ========== 步骤5: 对比分析 ==========
    print("\n" + "=" * 50)
    print("步骤5: 对比分析")
    print("=" * 50)

    pytorch_recon1_np = recon1_pytorch.cpu().detach().numpy()
    pytorch_recon2_np = recon2_pytorch.cpu().detach().numpy()

    recon_diff1 = np.mean(np.abs(pytorch_recon1_np - recon1_onnx))
    recon_diff2 = np.mean(np.abs(pytorch_recon2_np - recon2_onnx))

    print(f"重建图像差异: recon1={recon_diff1:.6f}, recon2={recon_diff2:.6f}")

    # 计算MSE
    pytorch_mse1 = np.mean((input1_np - pytorch_recon1_np) ** 2)
    pytorch_mse2 = np.mean((input2_np - pytorch_recon2_np) ** 2)
    onnx_mse1 = np.mean((input1_np - recon1_onnx) ** 2)
    onnx_mse2 = np.mean((input2_np - recon2_onnx) ** 2)

    print("\nMSE对比:")
    print(f"  PyTorch: MSE1={pytorch_mse1:.6f}, MSE2={pytorch_mse2:.6f}")
    print(f"  ONNX:    MSE1={onnx_mse1:.6f}, MSE2={onnx_mse2:.6f}")
    print(
        f"  差异:    MSE1={abs(pytorch_mse1 - onnx_mse1):.6f}, MSE2={abs(pytorch_mse2 - onnx_mse2):.6f}"
    )

    if recon_diff1 > 1e-5 or recon_diff2 > 1e-5:
        print("\n❌ 接收端存在显著差异！")
        print("可能的原因:")
        print("1. 交织器元数据重建不正确")
        print("2. 量化器状态不一致")
        print("3. 模型权重加载问题")
    else:
        print("\n✅ 接收端结果基本一致")


if __name__ == "__main__":
    main()
