import argparse

import onnx
import torch
import torch.onnx

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state


def parse_args():
    parser = argparse.ArgumentParser(description="Convert WITT model to ONNX")
    # 模型加载相关参数
    parser.add_argument(
        "--model_path",
        type=str,
        default="results/default_exp_phase3/models/celeba_phase3_epoch5_snr5.pth",
        help="Path to the trained model checkpoint",
    )

    # 数据集相关参数
    parser.add_argument(
        "--trainset",
        type=str,
        default="celeba",
        choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet"],
        help="Dataset used for training",
    )
    parser.add_argument(
        "--testset",
        type=str,
        default="kodak",
        choices=["kodak", "CLIC21"],
        help="Test dataset name",
    )
    parser.add_argument(
        "--data_path", type=str, default="./data", help="Path to the dataset"
    )

    # 模型相关参数
    parser.add_argument(
        "--model",
        type=str,
        default="WITT_W/O",
        choices=["WITT_W/O"],
        help="Model type",
    )
    parser.add_argument("--C", type=int, default=512, help="Bottleneck dimension")
    parser.add_argument("--quant_code_dim", type=int, default=128)

    # 通道相关参数
    parser.add_argument(
        "--channel_type",
        type=str,
        default="rayleigh",
        choices=["awgn", "rayleigh"],
        help="Channel type",
    )
    parser.add_argument(
        "--multiple_snr", type=str, default="5", help="SNR values for training"
    )

    # 损失函数相关参数
    parser.add_argument(
        "--distortion_metric",
        type=str,
        default="MSE",
        choices=["MSE", "MS-SSIM"],
        help="Distortion metric for evaluation",
    )

    # 实验相关参数
    parser.add_argument(
        "--experiment_name",
        type=str,
        default="default_exp",
        help="Name of the experiment",
    )
    parser.add_argument(
        "--num_workers", type=int, default=0, help="Number of workers for data loading"
    )
    return parser.parse_args()


def main():
    # 解析命令行参数
    args = parse_args()

    # 初始化配置 - 使用phase 3来确保所有组件都被正确初始化
    cfg = BaseConfig(args, training_phase=3)

    # 创建模型实例
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs["embed_dims"][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)

    # 加载模型权重
    print(f"Loading checkpoint from: {args.model_path}")
    checkpoint = torch.load(args.model_path, map_location=cfg.device)
    print("Checkpoint keys:", checkpoint.keys())

    # 加载模型状态
    if "net1_state_dict" in checkpoint:
        load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
        print("✅ net1 state loaded successfully")
    else:
        print("❌ net1_state_dict not found in checkpoint")

    if "net2_state_dict" in checkpoint:
        load_individual_model_state(net2, checkpoint["net2_state_dict"], cfg.device)
        print("✅ net2 state loaded successfully")
    else:
        print("❌ net2_state_dict not found in checkpoint")

    if "quantizer_state_dict" in checkpoint:
        load_individual_model_state(
            quantizer, checkpoint["quantizer_state_dict"], cfg.device
        )
        print("✅ quantizer state loaded successfully")
    else:
        print("❌ quantizer_state_dict not found in checkpoint")

    # 设置为评估模式
    net1.eval()
    net2.eval()
    quantizer.eval()
    interleaver.eval()

    # 创建虚拟输入
    batch_size = 1
    dummy_input1 = torch.randn(
        batch_size, 3, cfg.image_dims[1], cfg.image_dims[2], requires_grad=True
    ).to(cfg.device)
    dummy_input2 = torch.randn(
        batch_size, 3, cfg.image_dims[1], cfg.image_dims[2], requires_grad=True
    ).to(cfg.device)

    # 定义发送端模型
    class TransmitterModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer, interleaver):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer
            self.interleaver = interleaver

        def forward(self, input1, input2):
            # 编码 - WITT_W/O模型不需要SNR参数
            feature1 = self.net1.encode(input1)
            feature2 = self.net2.encode(input2)
            feature = feature1 + feature2
            B1, L1_feat, D1_feat = feature1.shape
            # 量化
            quantized_feature = self.quantizer.quantize_features_to_bits(
                feature.reshape(B1 * L1_feat, D1_feat)
            )
            quantized_feature = quantized_feature.reshape(
                B1, L1_feat, cfg.quant_code_dim
            )
            # 交织
            interleaved_feature, metadata = self.interleaver.interleave(
                quantized_feature
            )
            return interleaved_feature

    # 定义接收端模型
    class ReceiverModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer, interleaver, original_shape):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer
            self.interleaver = interleaver
            # 保存原始形状信息，用于构建metadata
            self.original_shape = original_shape

        def forward(self, interleaved_feature):
            # 为了ONNX兼容性，我们需要重建metadata
            # 根据交织器的逻辑，我们需要知道交织前的形状
            B, L = interleaved_feature.shape

            # 使用保存的原始形状信息来重建metadata
            # original_shape应该是 [seq_len, batch_size, feature_dim] 的形状
            seq_len, _, feature_dim = self.original_shape
            metadata = {"x_interleaved_shape": torch.Size([seq_len, B, feature_dim])}

            # 解交织
            deinterleaved_feature = self.interleaver.deinterleave(
                interleaved_feature, metadata
            )

            # 重新整形为量化器期望的格式
            B_new, seq_len_new, feature_dim_new = deinterleaved_feature.shape
            deinterleaved_feature = deinterleaved_feature.reshape(
                B_new * seq_len_new, feature_dim_new
            )

            # 反量化
            dequantized_feature = self.quantizer.dequantize_bits_to_features(
                deinterleaved_feature
            ).reshape(B_new, seq_len_new, -1)

            # 解码 - WITT_W/O模型不需要SNR参数
            recon_image1 = self.net1.decode(dequantized_feature)
            recon_image2 = self.net2.decode(dequantized_feature)
            return recon_image1, recon_image2

    # 首先运行一次前向传播来获取形状信息
    with torch.no_grad():
        # 获取编码后的特征形状
        feature1 = net1.encode(dummy_input1)
        feature2 = net2.encode(dummy_input2)
        feature = feature1 + feature2
        B1, L1_feat, D1_feat = feature1.shape

        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(
            feature.reshape(B1 * L1_feat, D1_feat)
        )
        quantized_feature = quantized_feature.reshape(B1, L1_feat, cfg.quant_code_dim)

        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        original_interleaved_shape = metadata["x_interleaved_shape"]

        print(f"Original feature shape: {feature.shape}")
        print(f"Quantized feature shape: {quantized_feature.shape}")
        print(f"Interleaved feature shape: {interleaved_feature.shape}")
        print(f"Original interleaved shape in metadata: {original_interleaved_shape}")

    # 创建发送端和接收端模型
    transmitter_model = TransmitterModel(net1, net2, quantizer, interleaver)
    receiver_model = ReceiverModel(
        net1, net2, quantizer, interleaver, original_interleaved_shape
    )

    # 导出发送端模型
    torch.onnx.export(
        transmitter_model,
        (dummy_input1, dummy_input2),
        "transmitter_model.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input1", "input2"],
        output_names=["interleaved_feature"],
        dynamic_axes={
            "input1": {0: "batch_size"},
            "input2": {0: "batch_size"},
            "interleaved_feature": {0: "batch_size"},
        },
    )

    # 导出接收端模型
    # 使用我们已经获得的交织特征形状信息
    actual_interleaved_dim = interleaved_feature.shape[1]
    print(f"实际交织特征维度: {actual_interleaved_dim}")

    dummy_interleaved = torch.randn(
        batch_size, actual_interleaved_dim, requires_grad=True
    ).to(cfg.device)

    # 测试接收端模型
    print("Testing receiver model...")
    with torch.no_grad():
        test_recon1, test_recon2 = receiver_model(interleaved_feature)
        print(f"Receiver output shapes: {test_recon1.shape}, {test_recon2.shape}")
        print(f"Expected output shape: {dummy_input1.shape}")

    torch.onnx.export(
        receiver_model,
        (dummy_interleaved,),
        "receiver_model.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["interleaved_feature"],
        output_names=["output1", "output2"],
        dynamic_axes={
            "interleaved_feature": {0: "batch_size"},
            "output1": {0: "batch_size"},
            "output2": {0: "batch_size"},
        },
    )

    # 验证模型
    print("\n" + "=" * 60)
    print("验证ONNX模型")
    print("=" * 60)

    try:
        onnx.checker.check_model("transmitter_model.onnx")
        print("✅ 发送端模型验证成功!")
    except onnx.checker.ValidationError as e:
        print(f"❌ 发送端模型验证失败: {e}")

    try:
        onnx.checker.check_model("receiver_model.onnx")
        print("✅ 接收端模型验证成功!")
    except onnx.checker.ValidationError as e:
        print(f"❌ 接收端模型验证失败: {e}")

    # 测试完整的端到端流程
    print("\n" + "=" * 60)
    print("测试端到端ONNX推理")
    print("=" * 60)

    try:
        import onnxruntime as ort

        # 创建ONNX推理会话
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")

        # 准备测试数据
        test_input1 = dummy_input1.cpu().detach().numpy().astype("float32")
        test_input2 = dummy_input2.cpu().detach().numpy().astype("float32")

        # 发送端推理
        transmitter_outputs = transmitter_session.run(
            None, {"input1": test_input1, "input2": test_input2}
        )
        interleaved_features = transmitter_outputs[0]

        # 接收端推理
        receiver_outputs = receiver_session.run(
            None, {"interleaved_feature": interleaved_features}
        )
        output1, output2 = receiver_outputs

        print("✅ 端到端推理成功!")
        print(f"   输入形状: {test_input1.shape}, {test_input2.shape}")
        print(f"   交织特征形状: {interleaved_features.shape}")
        print(f"   输出形状: {output1.shape}, {output2.shape}")

        # 计算重建误差
        import numpy as np

        mse1 = np.mean((test_input1 - output1) ** 2)
        mse2 = np.mean((test_input2 - output2) ** 2)
        print(f"   重建MSE: {mse1:.6f}, {mse2:.6f}")

    except ImportError:
        print("⚠️  onnxruntime未安装，跳过端到端测试")
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")

    print("\n" + "=" * 60)
    print("ONNX转换完成!")
    print("生成的文件:")
    print("  - transmitter_model.onnx (发送端模型)")
    print("  - receiver_model.onnx (接收端模型)")
    print("=" * 60)


if __name__ == "__main__":
    main()
