#!/usr/bin/env python3
"""
ONNX模型批量推理测试

支持发送端和接收端的完整批量推理，一次性处理整个batch的数据
"""

import numpy as np
import onnxruntime as ort
import time
import os
import glob
import random
from PIL import Image
import torchvision.transforms as transforms
from tqdm import tqdm
import argparse

def load_images_batch(image_paths, target_size=(64, 64)):
    """批量加载和预处理图像"""
    batch_tensors = []
    batch_images = []
    valid_paths = []
    
    transform = transforms.Compose([
        transforms.Resize(target_size),
        transforms.ToTensor(),
    ])
    
    for image_path in image_paths:
        try:
            image = Image.open(image_path).convert('RGB')
            tensor = transform(image)
            batch_tensors.append(tensor)
            batch_images.append(image)
            valid_paths.append(image_path)
        except Exception as e:
            print(f"跳过图像 {image_path}: {e}")
            continue
    
    if len(batch_tensors) == 0:
        return None, None, None
    
    import torch
    batch_tensor = torch.stack(batch_tensors, dim=0)
    return batch_tensor.numpy().astype(np.float32), batch_images, valid_paths

def get_image_files_from_directory(image_dir, max_images=None, random_seed=42):
    """从目录中获取图像文件列表"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']
    
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))
        image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))
    
    if len(image_files) == 0:
        print(f"在目录 {image_dir} 中没有找到图像文件")
        return []
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    if random_seed is not None:
        random.seed(random_seed)
        random.shuffle(image_files)
    
    if max_images is not None and max_images < len(image_files):
        image_files = image_files[:max_images]
        print(f"限制为前 {max_images} 个图像文件")
    
    return image_files

def tensor_to_image(tensor):
    """将张量转换为PIL图像"""
    tensor = np.clip(tensor, 0, 1)
    if tensor.ndim == 3:
        tensor = np.transpose(tensor, (1, 2, 0))
    tensor = (tensor * 255).astype(np.uint8)
    return Image.fromarray(tensor)

def calculate_metrics(original_batch, reconstructed_batch):
    """计算批量重构质量指标"""
    original_batch = np.clip(original_batch, 0, 1)
    reconstructed_batch = np.clip(reconstructed_batch, 0, 1)
    
    batch_size = original_batch.shape[0]
    mse_list = []
    psnr_list = []
    
    for i in range(batch_size):
        mse = np.mean((original_batch[i] - reconstructed_batch[i]) ** 2)
        mse_list.append(mse)
        
        if mse == 0:
            psnr = float('inf')
        else:
            psnr = 20 * np.log10(1.0 / np.sqrt(mse))
        psnr_list.append(psnr)
    
    return mse_list, psnr_list

def create_comparison_grid(original_images, reconstructed_images, output_path, max_display=16):
    """创建对比网格图像"""
    num_images = min(len(original_images), len(reconstructed_images), max_display)
    
    if num_images == 0:
        return
    
    cols = int(np.ceil(np.sqrt(num_images)))
    rows = int(np.ceil(num_images / cols))
    
    img_width, img_height = original_images[0].size
    grid_width = cols * img_width
    grid_height = rows * img_height * 2
    grid_image = Image.new('RGB', (grid_width, grid_height), color='white')
    
    # 粘贴原始图像（上半部分）
    for i in range(num_images):
        row = i // cols
        col = i % cols
        x = col * img_width
        y = row * img_height
        grid_image.paste(original_images[i], (x, y))
    
    # 粘贴重构图像（下半部分）
    for i in range(num_images):
        row = i // cols
        col = i % cols
        x = col * img_width
        y = (row + rows) * img_height
        
        recon_img = reconstructed_images[i]
        if recon_img.size != original_images[i].size:
            recon_img = recon_img.resize(original_images[i].size, Image.LANCZOS)
        
        grid_image.paste(recon_img, (x, y))
    
    grid_image.save(output_path)
    print(f"保存对比网格: {output_path}")

def test_batch_inference(image_dir, batch_size=8, max_images=32, output_dir="./batch_results"):
    """完整的批量推理测试"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载ONNX模型
    print("加载ONNX模型...")
    try:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")
        print("成功加载发送端和接收端模型")
    except Exception as e:
        print(f"模型加载失败: {e}")
        return False
    
    # 获取图像文件列表
    image_files = get_image_files_from_directory(image_dir, max_images)
    if len(image_files) == 0:
        return False
    
    # 确保图像数量是偶数
    if len(image_files) % 2 != 0:
        image_files = image_files[:-1]
    
    # 分成两组
    image1_files = image_files[::2]
    image2_files = image_files[1::2]
    
    total_pairs = len(image1_files)
    print(f"将处理 {total_pairs} 个图像对，批次大小: {batch_size}")
    
    # 统计信息
    all_mse1, all_mse2 = [], []
    all_psnr1, all_psnr2 = [], []
    total_transmitter_time = 0
    total_receiver_time = 0
    processed_images = 0
    
    # 用于保存示例结果
    sample_originals1, sample_originals2 = [], []
    sample_reconstructed1, sample_reconstructed2 = [], []
    
    # 按批次处理
    num_batches = (total_pairs + batch_size - 1) // batch_size
    
    for batch_idx in tqdm(range(num_batches), desc="处理批次"):
        start_idx = batch_idx * batch_size
        end_idx = min(start_idx + batch_size, total_pairs)
        
        batch_image1_files = image1_files[start_idx:end_idx]
        batch_image2_files = image2_files[start_idx:end_idx]
        
        try:
            # 批量加载图像
            batch1_tensor, batch1_images, _ = load_images_batch(batch_image1_files)
            batch2_tensor, batch2_images, _ = load_images_batch(batch_image2_files)
            
            if batch1_tensor is None or batch2_tensor is None:
                print(f"跳过批次 {batch_idx}: 图像加载失败")
                continue
            
            # 确保两个批次大小相同
            actual_batch_size = min(batch1_tensor.shape[0], batch2_tensor.shape[0])
            batch1_tensor = batch1_tensor[:actual_batch_size]
            batch2_tensor = batch2_tensor[:actual_batch_size]
            
            print(f"批次 {batch_idx}: 处理 {actual_batch_size} 个图像对")
            
            # 发送端批量推理
            start_time = time.time()
            transmitter_outputs = transmitter_session.run(
                None, {"input1": batch1_tensor, "input2": batch2_tensor}
            )
            interleaved_features = transmitter_outputs[0]
            transmitter_time = time.time() - start_time
            total_transmitter_time += transmitter_time
            
            print(f"  发送端推理: {transmitter_time:.4f}秒, 输出形状: {interleaved_features.shape}")
            
            # 接收端批量推理
            start_time = time.time()
            receiver_outputs = receiver_session.run(
                None, {"interleaved_feature": interleaved_features}
            )
            output1_batch, output2_batch = receiver_outputs
            receiver_time = time.time() - start_time
            total_receiver_time += receiver_time
            
            print(f"  接收端推理: {receiver_time:.4f}秒, 输出形状: {output1_batch.shape}, {output2_batch.shape}")
            
            # 计算质量指标
            mse1_batch, psnr1_batch = calculate_metrics(batch1_tensor, output1_batch)
            mse2_batch, psnr2_batch = calculate_metrics(batch2_tensor, output2_batch)
            
            all_mse1.extend(mse1_batch)
            all_mse2.extend(mse2_batch)
            all_psnr1.extend(psnr1_batch)
            all_psnr2.extend(psnr2_batch)
            
            # 保存示例图像
            if len(sample_originals1) < 16:
                for i in range(min(actual_batch_size, 16 - len(sample_originals1))):
                    sample_originals1.append(batch1_images[i])
                    sample_originals2.append(batch2_images[i])
                    sample_reconstructed1.append(tensor_to_image(output1_batch[i]))
                    sample_reconstructed2.append(tensor_to_image(output2_batch[i]))
            
            processed_images += actual_batch_size
            
            # 显示当前批次统计
            batch_avg_psnr1 = np.mean(psnr1_batch)
            batch_avg_psnr2 = np.mean(psnr2_batch)
            batch_total_time = transmitter_time + receiver_time
            batch_throughput = actual_batch_size / batch_total_time
            
            print(f"  批次平均PSNR: 图像1={batch_avg_psnr1:.2f} dB, 图像2={batch_avg_psnr2:.2f} dB")
            print(f"  批次吞吐量: {batch_throughput:.2f} 图像对/秒")
            
        except Exception as e:
            print(f"处理批次 {batch_idx} 时出错: {e}")
            continue
    
    if processed_images == 0:
        print("没有成功处理任何图像")
        return False
    
    # 创建对比网格图像
    if sample_originals1:
        print("创建对比网格图像...")
        create_comparison_grid(
            sample_originals1, sample_reconstructed1,
            os.path.join(output_dir, "comparison_1.png")
        )
        create_comparison_grid(
            sample_originals2, sample_reconstructed2,
            os.path.join(output_dir, "comparison_2.png")
        )
    
    # 计算总体统计信息
    avg_mse1 = np.mean(all_mse1)
    avg_mse2 = np.mean(all_mse2)
    avg_psnr1 = np.mean(all_psnr1)
    avg_psnr2 = np.mean(all_psnr2)
    std_psnr1 = np.std(all_psnr1)
    std_psnr2 = np.std(all_psnr2)
    
    total_time = total_transmitter_time + total_receiver_time
    avg_time_per_image = total_time / processed_images
    throughput = processed_images / total_time
    
    # 保存详细报告
    report_path = os.path.join(output_dir, "batch_inference_report.txt")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("ONNX模型批量推理测试报告\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"图像目录: {image_dir}\n")
        f.write(f"批次大小: {batch_size}\n")
        f.write(f"处理的图像数量: {processed_images}\n")
        f.write(f"处理的批次数量: {num_batches}\n\n")
        
        f.write("推理时间统计:\n")
        f.write(f"  总发送端时间: {total_transmitter_time:.4f}秒\n")
        f.write(f"  总接收端时间: {total_receiver_time:.4f}秒\n")
        f.write(f"  总处理时间: {total_time:.4f}秒\n")
        f.write(f"  平均每图像时间: {avg_time_per_image:.4f}秒\n")
        f.write(f"  批量吞吐量: {throughput:.2f} 图像/秒\n\n")
        
        f.write("质量指标统计:\n")
        f.write(f"  图像1 - 平均MSE: {avg_mse1:.6f} ± {np.std(all_mse1):.6f}\n")
        f.write(f"  图像1 - 平均PSNR: {avg_psnr1:.2f} ± {std_psnr1:.2f} dB\n")
        f.write(f"  图像2 - 平均MSE: {avg_mse2:.6f} ± {np.std(all_mse2):.6f}\n")
        f.write(f"  图像2 - 平均PSNR: {avg_psnr2:.2f} ± {std_psnr2:.2f} dB\n")
        f.write(f"  总体平均PSNR: {(avg_psnr1 + avg_psnr2) / 2:.2f} dB\n\n")
        
        f.write("PSNR分布:\n")
        f.write(f"  图像1 - 范围: {np.min(all_psnr1):.2f} - {np.max(all_psnr1):.2f} dB\n")
        f.write(f"  图像2 - 范围: {np.min(all_psnr2):.2f} - {np.max(all_psnr2):.2f} dB\n\n")
        
        f.write("批处理效率:\n")
        f.write(f"  批次大小: {batch_size}\n")
        f.write(f"  平均每批次时间: {total_time / num_batches:.4f}秒\n")
        f.write(f"  发送端批处理加速: 是\n")
        f.write(f"  接收端批处理加速: 是\n")
    
    # 打印总结
    print(f"\n批量推理测试完成!")
    print(f"处理的图像数量: {processed_images}")
    print(f"批次大小: {batch_size}")
    print(f"平均PSNR: 图像1={avg_psnr1:.2f}±{std_psnr1:.2f} dB, 图像2={avg_psnr2:.2f}±{std_psnr2:.2f} dB")
    print(f"总体平均PSNR: {(avg_psnr1 + avg_psnr2) / 2:.2f} dB")
    print(f"批量吞吐量: {throughput:.2f} 图像/秒")
    print(f"平均每图像时间: {avg_time_per_image:.4f}秒")
    print(f"结果保存在: {output_dir}")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="ONNX模型批量推理测试")
    
    parser.add_argument("--image_dir", type=str, required=True, help="图像目录路径")
    parser.add_argument("--batch_size", type=int, default=8, help="批次大小")
    parser.add_argument("--max_images", type=int, default=32, help="最大处理图像数量")
    parser.add_argument("--output_dir", type=str, default="./batch_results", help="输出目录")
    
    args = parser.parse_args()
    
    print("ONNX模型批量推理测试")
    print("=" * 50)
    print(f"批次大小: {args.batch_size}")
    print(f"最大图像数: {args.max_images}")
    
    # 检查模型文件
    if not (os.path.exists("transmitter_model.onnx") and os.path.exists("receiver_model.onnx")):
        print("ONNX模型文件不存在!")
        print("请先运行 convert_to_onnx.py 生成ONNX模型")
        return 1
    
    # 检查图像目录
    if not os.path.exists(args.image_dir):
        print(f"图像目录不存在: {args.image_dir}")
        return 1
    
    # 执行批量推理测试
    success = test_batch_inference(
        args.image_dir,
        args.batch_size,
        args.max_images,
        args.output_dir
    )
    
    if success:
        print("\n批量测试完成!")
        print(f"请查看输出目录: {args.output_dir}")
        print("\n推荐查看:")
        print(f"  - {args.output_dir}/batch_inference_report.txt - 详细统计报告")
        print(f"  - {args.output_dir}/comparison_*.png - 对比图像")
        return 0
    else:
        print("\n批量测试失败!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    exit(exit_code)
